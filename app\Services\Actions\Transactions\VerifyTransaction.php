<?php

namespace App\Services\Actions\Transactions;

use App\Enums\PayTypeEnum;
use App\Exceptions\PaymentFailException;
use App\Models\Shopping\Invoice;
use App\Models\Shopping\Transaction;
use App\Models\User\WalletTransaction;
use App\Services\Payment\Contracts\PaymentContract;
use Illuminate\Support\Facades\DB;
use App\Services\Actions\Invoice\FinalizeInvoicePurchase;
use App\Services\Notifications\UserNotificationService;
/**
 * Verify Transaction Action
 *
 * Handles the verification of a payment transaction after the user completes the payment process.
 */
class VerifyTransaction
{
    /**
     * Handle the transaction verification process
     *
     * @param array $data The validated data from the request
     * @return Transaction The updated transaction model
     * @throws \Exception If transaction verification fails
     */
    public function handle(array $data): Transaction
    {
        $authority = $data['authority'];
        $status = $data['status'];

        $transaction = Transaction::where('authority', $authority)->first();
        if ($status == 'NOK') {
            throw new PaymentFailException(
                errorData: ["model" => $transaction],
            );
        }
        if ($transaction->status == PayTypeEnum::PAID) {
            return $transaction;
        }
        return DB::transaction(function () use ($data) {
            $authority = $data['authority'];

            $transaction = Transaction::where('authority', $authority)->first();

            $verification = $this->verifyTransaction($transaction, $authority);

            $isPaid = $this->updateTransactionStatus($transaction, $verification);

            if ($isPaid) {
                if ($transaction->payable_type == Invoice::class) {

                    $this->processSuccessfulInvoicePayment($transaction);

                } else {
                    $this->processSuccessfulWalletTopUp($transaction);

                }
            }



            return $transaction;
        });
    }


    /**
     * Verify the transaction with the payment provider
     *
     * @param Transaction $transaction
     * @param string $authority
     * @return array
     */
    private function verifyTransaction(Transaction $transaction, string $authority): array
    {
        $paymentDriver = app(PaymentContract::class);

        $verifyData = [
            'authority' => $authority,
            'amount' => $transaction->amount
        ];

        return $paymentDriver->verifyPayment($verifyData);
    }

    /**
     * Update the transaction based on verification data
     *
     * @param Transaction $transaction
     * @param array $verification
     * @return bool Whether the transaction was paid
     */
    private function updateTransactionStatus(Transaction $transaction, array $verification): bool
    {
        $responseData = $verification['data'] ?? [];
        $isPaid = in_array($responseData['code'] ?? 0, [100, 101]);

        $transaction->update([
            'status' => $isPaid ? PayTypeEnum::PAID : PayTypeEnum::REJECTED,
            'ref_id' => $responseData['ref_id'] ?? null,
            'card_number' => $responseData['card_pan'] ?? null,
            'paid_at' => $isPaid ? now() : null,
            'wages' => $responseData['wages'] ?? null,
            'code' => $responseData['code'] ?? null,
            'message' => $responseData['message'] ?? null,
            'card_hash' => $responseData['card_hash'] ?? null,
            'fee_type' => $responseData['fee_type'] ?? null,
            'fee' => $responseData['fee'] ?? null,
            'shaparak_fee' => $responseData['shaparak_fee'] ?? null,
            'order_id' => $responseData['order_id'] ?? null,
        ]);

        return $isPaid;
    }



    /**
     * Process actions after a successful invoice payment:
     * 1. Create purchase entries with negative values for each invoice product
     * 2. Confirm reservations by updating their status
     * 3. Send notification to user
     *
     * @param Transaction $transaction
     * @return void
     */
    private function processSuccessfulInvoicePayment(Transaction $transaction): void
    {
        if (!$transaction->payable) {
            return;
        }
        $transaction->payable->update(['status' => PayTypeEnum::PAID]);

        /** @var \App\Models\Shopping\Invoice $invoice */
        $invoice = $transaction->payable;

        app(FinalizeInvoicePurchase::class)->handle($invoice);

        // Send notification to user
        app(UserNotificationService::class)->sendInvoicePaymentSuccess($invoice, $transaction);
    }

    /**
     * Process actions after a successful wallet top-up:
     * Update the wallet transaction status and send notification
     *
     * @param Transaction $transaction
     * @return void
     */
    private function processSuccessfulWalletTopUp(Transaction $transaction): void
    {
        // First create a wallet transaction record
        $walletTransaction = WalletTransaction::create([
            'user_id' => $transaction->user_id,
            'type' => WalletTransaction::TYPE_DEPOSIT,
            'amount' => $transaction->amount,
            'description' => $transaction->description,
            'referenceable_id' => $transaction->id,
            'referenceable_type' => get_class($transaction),
        ]);

        $transaction->update([
            'payable_id' => $walletTransaction->id,
            'payable_type' => get_class($walletTransaction),
        ]);

        // Send notification to user
        app(UserNotificationService::class)->sendWalletTopupSuccess($transaction);
    }


}
