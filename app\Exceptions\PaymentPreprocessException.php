<?php

namespace App\Exceptions;

use Symfony\Component\HttpFoundation\Response;

/**
 * Payment Preprocess Exception
 *
 * Used for errors that occur during the payment preprocessing phase,
 * such as communication errors with payment gateways or invalid payment data.
 */
class PaymentPreprocessException extends BusinessException
{
    /**
     * Create a new payment preprocess exception instance.
     *
     * @param string $message The error message
     * @param array $errorData Additional error data
     * @param int $statusCode The HTTP status code
     * @param \Throwable|null $previous The previous exception
     * @return void
     */
    public function __construct(
        string $message = 'خطا در پیش‌پردازش پرداخت',
        array $errorData = [],
        int $statusCode = Response::HTTP_BAD_REQUEST,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $errorData, $statusCode, $previous);
    }
}
