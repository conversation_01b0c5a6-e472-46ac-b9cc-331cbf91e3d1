<?php

namespace App\Http\Requests\Product;

use App\Models\Product\Product;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Get Product Guarantees Request
 *
 * Validates the request data for retrieving guarantees related to a product.
 */
class GetProductGuaranteesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so anyone can access it
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_slug' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $product = Product::where('slug', $value)->first();

                    if (!$product) {
                        $fail(__('messages.product.not_found'));
                        return;
                    }

                    // Store product in request for later use
                    $this->merge(['product' => $product]);
                },
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Add the product slug from the route parameter
        $this->merge([
            'product_slug' => $this->route('slug'),
        ]);
    }
}
