<?php

return [
    // Only the validation rules you're actually using
    'required' => 'فیلد :attribute الزامی است.',
    'string' => 'فیلد :attribute باید یک رشته متنی باشد.',
    'array' => 'فیلد :attribute باید یک آرایه باشد.',
    'in' => 'مقدار انتخاب شده برای :attribute معتبر نیست.',
    'enum' => 'مقدار انتخاب شده برای :attribute معتبر نیست.',
    'nullable' => 'فیلد :attribute می‌تواند خالی باشد.',
    'integer' => 'فیلد :attribute باید یک عدد صحیح باشد.',
    'numeric' => 'فیلد :attribute باید یک عدد باشد.',
    'boolean' => 'فیلد :attribute باید true یا false باشد.',
    'sometimes' => 'فیلد :attribute در صورت وجود باید معتبر باشد.',
    'exists' => 'مقدار انتخاب شده برای :attribute معتبر نیست.',
    'digits' => 'فیلد :attribute باید دقیقاً :digits رقم باشد.',
    'min' => [
        'numeric' => 'فیلد :attribute باید حداقل :min باشد.',
        'string' => 'فیلد :attribute باید حداقل :min کاراکتر باشد.',
    ],
    'max' => [
        'numeric' => 'فیلد :attribute نمی‌تواند بیشتر از :max باشد.',
        'string' => 'فیلد :attribute نمی‌تواند بیشتر از :max کاراکتر باشد.',
    ],
    'url' => 'مقدارهای داخل فیلد :attribute باید لینک معتبر باشند.',


    'required_without' => 'فیلد :attribute زمانی که :values وارد نشده باشد نیاز است',
    // Attribute names in Persian (only the ones you're using)
    'attributes' => [
        'bank_id' => 'شناسه بانک',
        'account_number' => 'شماره حساب',
        'card_number' => 'شماره کارت',
        'sheba_number' => 'شماره شبا',
        'shamsi_birth_date' => 'تاریخ تولد',
        'in_stock_only' => 'موجود در انبار',
        'has guarantee only' => 'فقط با گارانتی',
        'target' => 'هدف',
        'type' => 'نوع',
        'title' => 'عنوان',
        'body' => 'متن',
        'image' => 'تصویر',
        'payload' => 'محتوای اضافی',
        'time_to_send' => 'زمان ارسال',
        'token' => 'توکن',
        'topic' => 'موضوع',
        'path' => 'مسیر',
        'limit' => 'تعداد محصولات مشابه',
        'slug' => 'شناسه',
        'payment_method' => 'روش پرداخت',
        // Cart related attributes
        'variant_id' => 'شناسه نوع محصول',
        'product_id' => 'شناسه محصول',
        'quantity' => 'تعداد',
        'user_id' => 'شناسه کاربر',
        'cart' => 'سبد خرید',
        //product'
        'product' => 'محصول',
        'meta_keywords' => 'کلمات کلیدی',
        "shop_id" => "شناسه فروشگاه",
        // Address related attributes
        'name' => 'نام آدرس',
        'receiver_name' => 'نام گیرنده',
        'receiver_phone' => 'شماره تماس گیرنده',
        'is_recipient_self' => 'گیرنده خودم هستم',
        'province' => 'استان',
        'city' => 'شهر',
        'zip_code' => 'کد پستی',
        'address' => 'آدرس',
        'latitude' => 'عرض جغرافیایی',
        'longitude' => 'طول جغرافیایی',
        'address_id' => 'شناسه آدرس',
        'invoice_id' => 'شناسه فاکتور',
        'status' => 'وضعیت',
        'category_id' => 'شناسه دسته‌بندی',
        //Torob
        'page_urls' => 'لینک‌های صفحات',
        'page_uniques' => 'شناسه‌های صفحات',
        'page' => "صفحه",
        'sort' => 'مرتب‌سازی',
        'price' => 'قیمت',
        'stock' => 'تعداد',
        "article_id" => "شناسه آرتیکل"
    ],

    'min_jalali_age' => 'از :attribute باید حداقل :years سال گذشته باشد.',
];
