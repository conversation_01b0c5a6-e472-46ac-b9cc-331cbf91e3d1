<?php

namespace App\Http\Middleware;

use App\Models\User\User;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

/**
 * JWT Authentication Middleware
 *
 * This middleware extracts the API key from the Authorization header,
 * finds the corresponding user, and attaches the user to the request.
 * Each user has their own unique key for authentication.
 */
class JwtMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // Check if key exists in the Authorization header
            $authHeader = $request->header('Authorization');
            if (!$authHeader) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.key_not_found')
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Extract key from Bearer format
            $key = str_replace('Bearer ', '', $authHeader);
            if (empty($key)) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.invalid_key')
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Find the user based on the API key
            $user = User::where('api_key', $key)->first();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.user_not_found')
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Set the user in the Auth facade
            Auth::login($user);

            // Also attach user to the request for backward compatibility
            $request->merge(['user' => $user]);
            $request->setUserResolver(function () use ($user) {
                return $user;
            });

            return $next($request);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('messages.auth.jwt_error', ['message' => $e->getMessage()])
            ], Response::HTTP_UNAUTHORIZED);
        }
    }
}
