<?php

namespace App\Http\Controllers\Api\V1\Private\Shopping;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Invoice\CreateInvoice;
use App\Services\Actions\Invoice\GetUserInvoices;
use App\Services\Actions\Invoice\InvoiceShowAction;
use App\Http\Resources\Shopping\InvoiceResource;
use App\Http\Requests\Invoice\CreateInvoiceRequest;
use App\Http\Requests\Invoice\GetUserInvoicesRequest;
use App\Http\Requests\Invoice\ShowInvoiceRequest;
use App\Http\Resources\Core\PaginatedResourceCollection;

/**
 * Controller for managing and retrieving invoice information
 *
 * @group Invoice Management
 * @authenticated
 */
class InvoiceController extends BaseController
{
    /**
     * Get all invoices for the authenticated user with delivery status counts
     *
     * @param GetUserInvoicesRequest $request The validated request
     * @param GetUserInvoices $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetUserInvoicesRequest $request, GetUserInvoices $action)
    {
        $result = $action->handle($request->validated());

        $response = new PaginatedResourceCollection($result['invoices'], "invoices", InvoiceResource::class);

        // Add delivery status counts to the response
        $responseData = $response->toArray($request);
        $responseData['delivery_status_counts'] = $result['delivery_status_counts'];

        return $this->sendResponse(
            $responseData,
            __('messages.invoice.found')
        );
    }

    /**
     * Create a new invoice for the authenticated user
     *
     * @param CreateInvoiceRequest $request The validated request
     * @param CreateInvoice $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateInvoiceRequest $request, CreateInvoice $action)
    {
        $invoice = $action->handle($request->validated());

        return $this->sendResponse(
            new InvoiceResource($invoice),
            __('messages.invoice.created')
        );
    }

    /**
     * Get a specific invoice by ID
     *
     * @param ShowInvoiceRequest $request The validated request
     * @param InvoiceShowAction $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(ShowInvoiceRequest $request, InvoiceShowAction $action)
    {
        $invoice = $action->handle($request->validated());

        return $this->sendResponse(
            new InvoiceResource($invoice),
            __('messages.invoice.detail_found')
        );
    }
}
