<?php

namespace App\Http\Controllers\Api\V1\Private\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\ProductAttributesRequest;
use App\Http\Resources\Product\ProductResource;
use App\Services\Actions\Product\UpdateProductAttributes;
use Symfony\Component\HttpFoundation\JsonResponse;

class ProductAttributeController extends BaseController
{
    /**
     * Store product attributes
     *
     * @param \App\Http\Requests\Product\ProductAttributesRequest $request 
     * @param \App\Services\Actions\Product\UpdateProductAttributes $action The action that handles the Store logic.
     * @return \Illuminate\Http\JsonResponse The response containing the updated product resource.
     */
    public function store(ProductAttributesRequest $request, UpdateProductAttributes $action): JsonResponse
    {
        $data = $action->handle($request->validated());

        return $this->sendResponse(
            new ProductResource($data),
            __('messages.product.updated')
        );
    }
}
