<?php

namespace App\Rules;

use Closure;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\ValidationRule;
use <PERSON><PERSON><PERSON><PERSON>ser\Verta\Verta;

class MinJalaliAge implements ValidationRule
{
    /**
     * @param  int     $years   Minimum number of elapsed years (e.g. 18)
     * @param  string  $format  Expected input format (default 1404/04/07)
     */
    public function __construct(
        protected int $years,
        protected string $format = 'Y/m/d',
    ) {
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): void  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {


        try {
            $jalali = Verta::parse($value);

            $gregorian = Carbon::instance($jalali->datetime());
            $age = $gregorian->diffInYears();
            if ($age < $this->years) {
                $fail(__('validation.min_jalali_age', [
                    'attribute' => __("validation.attributes.$attribute"),
                    'years' => $this->years,
                ]));
            }
        } catch (\Throwable) {
            $fail('The :attribute is not a valid Jalali date.');
        }
    }
}
