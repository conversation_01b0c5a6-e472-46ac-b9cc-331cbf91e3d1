<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Category;
use App\Models\Product\Product;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

/**
 * Service class to retrieve products by category with filtering, sorting, and pagination.
 * 
 * This class handles complex MongoDB aggregation queries to fetch products with their
 * variants, attributes, images, guarantees, and related data.
 */
class GetCategoryProducts
{
    private ?Category $category = null;
    private array $aggregationPipeline = [];

    /**
     * Main entry point to handle category products request.
     *
     * @param array $data The validated request data containing filters, pagination, etc.
     * @return array Contains paginated products, total count, and price statistics
     */
    public function handle(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $this->initializeCategory($data['slug']);
            $this->buildAggregationPipeline($data);

            return $this->executeQueryAndFormatResults($data);
        });
    }

    /**
     * Initialize the category based on slug.
     */
    private function initializeCategory(string $slug): void
    {
        $this->category = Category::where('slug', $slug)->first();

        if (!$this->category) {
            throw new \Exception("Category not found: {$slug}");
        }
    }

    /**
     * Build the complete MongoDB aggregation pipeline.
     */
    private function buildAggregationPipeline(array $data): void
    {
        $this->aggregationPipeline = [];

        // Apply filters in logical order
        $this->addSearchFilter($data);
        $this->addBaseCategoryQuery();
        $this->addProductLookups();
        $this->addPriceCalculations();
        $this->addBusinessFilters($data);
        $this->addAttributeFilters($data);
        $this->addSorting($data);
        $this->addPaginationFacet($data);
    }

    /**
     * Execute the aggregation query and format results.
     */
    private function executeQueryAndFormatResults(array $data): array
    {
        // Get price statistics before pagination
        $priceStats = $this->calculatePriceStatistics();

        // Execute main query
        $result = Product::raw(function ($collection) {
            return $collection->aggregate($this->aggregationPipeline);
        })->first();

        $products = $result['data'] ?? [];
        $totalCount = $result['count'][0]['total'] ?? 0;

        $paginator = $this->createPaginator($products, $totalCount, $data);

        return [
            'products' => $paginator,
            'min_price' => $priceStats['min_price'],
            'max_price' => $priceStats['max_price'],
        ];
    }

    /**
     * Add text search filter to the pipeline.
     */
    private function addSearchFilter(array $data): void
    {
        if (!isset($data['search']) || empty($data['search'])) {
            return;
        }

        $searchTerm = $data['search'];
        $this->aggregationPipeline[] = [
            '$match' => [
                '$or' => [
                    ['$text' => ['$search' => $searchTerm]],
                    ['title' => ['$regex' => '^' . $searchTerm, '$options' => 'i']],
                ]
            ]
        ];
    }

    /**
     * Add base category filtering to the pipeline.
     */
    private function addBaseCategoryQuery(): void
    {
        $categoryIds = $this->getAllCategoryIds($this->category);

        $this->aggregationPipeline[] = [
            '$match' => [
                'category_ids' => ['$in' => $categoryIds]
            ]
        ];

        $this->aggregationPipeline[] = [
            '$addFields' => [
                'product_id_string' => ['$toString' => '$id']
            ]
        ];
    }

    /**
     * Add all necessary lookups for related data.
     */
    private function addProductLookups(): void
    {
        $this->addVariationsLookup();
        $this->addAttributesLookup();
        $this->addImagesLookup();
        $this->addGuaranteesLookup();
        $this->addCategoriesLookup();
        $this->addSalesDataLookup();
        $this->addCommentsLookup();

        // Filter products that have at least one variation with price > 0
        $this->addValidProductsFilter();
    }

    /**
     * Add product variants lookup.
     */
    private function addVariationsLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'product_variations',
                'localField' => 'product_id_string',
                'foreignField' => 'product_id',
                'as' => 'variants'
            ]
        ];
    }

    /**
     * Add variation attributes lookup.
     */
    private function addAttributesLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'variation_attributes',
                'let' => [
                    'variant_id_strings' => [
                        '$map' => [
                            'input' => '$variants',
                            'as' => 'v',
                            'in' => ['$toString' => '$$v.id']
                        ]
                    ]
                ],
                'pipeline' => [
                    [
                        '$match' => [
                            '$expr' => [
                                '$in' => ['$variation_id', '$$variant_id_strings']
                            ]
                        ]
                    ]
                ],
                'as' => 'variation_attributes'
            ]
        ];
    }

    /**
     * Add product images lookup.
     */
    private function addImagesLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'galleries',
                'let' => ['productIdStr' => '$product_id_string'],
                'pipeline' => [
                    [
                        '$match' => [
                            '$expr' => [
                                '$and' => [
                                    ['$eq' => ['$imageable_id', '$$productIdStr']],
                                    ['$eq' => ['$imageable_type', 'App\\Models\\Product\\Product']]
                                ]
                            ]
                        ]
                    ]
                ],
                'as' => 'gallery'
            ]
        ];
    }

    /**
     * Add guarantees lookup.
     */
    private function addGuaranteesLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'guarantees',
                'localField' => 'product_id_string',
                'foreignField' => 'product_id',
                'as' => 'guarantees'
            ]
        ];
    }

    /**
     * Add categories lookup with ObjectId conversion.
     */
    private function addCategoriesLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$addFields' => [
                'category_ids' => [
                    '$map' => [
                        'input' => '$category_ids',
                        'as' => 'catId',
                        'in' => ['$toObjectId' => '$$catId']
                    ]
                ]
            ]
        ];

        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'categories',
                'localField' => 'category_ids',
                'foreignField' => 'id',
                'as' => 'categories'
            ]
        ];
    }

    /**
     * Add sales data lookups (invoices and invoice_products).
     */
    private function addSalesDataLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'invoice_products',
                'localField' => 'product_id_string',
                'foreignField' => 'product_id',
                'as' => 'invoice_links'
            ]
        ];

        $this->aggregationPipeline[] = [
            '$addFields' => [
                'invoice_links' => [
                    '$map' => [
                        'input' => '$invoice_links',
                        'as' => 'link',
                        'in' => [
                            '$mergeObjects' => [
                                '$$link',
                                ['invoice_object_id' => ['$toObjectId' => '$$link.invoice_id']]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'invoices',
                'localField' => 'invoice_links.invoice_object_id',
                'foreignField' => 'id',
                'as' => 'invoices'
            ]
        ];
    }

    /**
     * Add comments lookup for ratings.
     */
    private function addCommentsLookup(): void
    {
        $this->aggregationPipeline[] = [
            '$lookup' => [
                'from' => 'comments',
                'localField' => 'product_id_string',
                'foreignField' => 'commentable_id',
                'as' => 'comments'
            ]
        ];
    }

    /**
     * Filter products that have valid variants (price > 0).
     */
    private function addValidProductsFilter(): void
    {
        $this->aggregationPipeline[] = [
            '$match' => [
                '$expr' => [
                    '$gt' => [
                        [
                            '$size' => [
                                '$filter' => [
                                    'input' => '$variants',
                                    'as' => 'v',
                                    'cond' => ['$gt' => ['$$v.price', 0]]
                                ]
                            ]
                        ],
                        0
                    ]
                ]
            ]
        ];
    }

    /**
     * Add price calculations (min/max price and average rating).
     */
    private function addPriceCalculations(): void
    {
        $priceCalculation = [
            '$map' => [
                'input' => [
                    '$filter' => [
                        'input' => '$variants',
                        'as' => 'v',
                        'cond' => [
                            '$gt' => [
                                ['$ifNull' => ['$$v.sale_price', '$$v.price']],
                                0
                            ]
                        ]
                    ]
                ],
                'as' => 'v',
                'in' => [
                    '$cond' => [
                        ['$gt' => ['$$v.sale_price', 0]],
                        '$$v.sale_price',
                        '$$v.price'
                    ]
                ]
            ]
        ];

        $this->aggregationPipeline[] = [
            '$addFields' => [
                'min_price' => ['$min' => $priceCalculation],
                'max_price' => ['$max' => $priceCalculation],
                'average_rating' => ['$avg' => '$comments.rate']
            ]
        ];
    }

    /**
     * Add business logic filters (price range, stock, guarantee).
     */
    private function addBusinessFilters(array $data): void
    {
        $this->addPriceRangeFilter($data);
        $this->addStockFilter($data);
        $this->addGuaranteeFilter($data);
    }

    /**
     * Add price range filter.
     */
    private function addPriceRangeFilter(array $data): void
    {
        if (!isset($data['min_price']) && !isset($data['max_price'])) {
            return;
        }

        $matchCondition = [];

        if (isset($data['min_price'])) {
            $matchCondition['$gte'] = (float) $data['min_price'];
        }

        if (isset($data['max_price'])) {
            $matchCondition['$lte'] = (float) $data['max_price'];
        }

        if (!empty($matchCondition)) {
            $this->aggregationPipeline[] = [
                '$match' => ['min_price' => $matchCondition]
            ];
        }
    }

    /**
     * Add stock availability filter.
     */
    private function addStockFilter(array $data): void
    {
        if (empty($data['in_stock_only']) || $data['in_stock_only'] != 'true') {
            return;
        }

        $this->aggregationPipeline[] = [
            '$match' => [
                '$expr' => [
                    '$gt' => [
                        [
                            '$size' => [
                                '$filter' => [
                                    'input' => '$variants',
                                    'as' => 'v',
                                    'cond' => [
                                        '$and' => [
                                            ['$gt' => [['$ifNull' => ['$$v.sale_price', '$$v.price']], 0]],
                                            ['$gt' => ['$$v.stock', 0]]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        0
                    ]
                ]
            ]
        ];
    }

    /**
     * Add guarantee availability filter.
     */
    private function addGuaranteeFilter(array $data): void
    {
        if (empty($data['has_guarantee_only']) || $data['has_guarantee_only'] != 'true') {
            return;
        }

        $this->aggregationPipeline[] = [
            '$match' => [
                '$expr' => [
                    '$gt' => [
                        ['$size' => ['$ifNull' => ['$guarantees', []]]],
                        0
                    ]
                ]
            ]
        ];
    }

    /**
     * Add attribute-based filters.
     */
    private function addAttributeFilters(array $data): void
    {
        $attributeFilters = $this->extractAttributeFilters($data);

        foreach ($attributeFilters as $attributeType => $attributeValue) {
            if (!is_array($attributeValue)) {
                $attributeValue = [$attributeValue];
            }

            $this->aggregationPipeline[] = [
                '$match' => [
                    'variation_attributes' => [
                        '$elemMatch' => [
                            'attribute_type' => $attributeType,
                            'attribute_value' => ['$in' => $attributeValue]
                        ]
                    ]
                ]
            ];
        }
    }

    /**
     * Extract attribute filters from request data.
     */
    private function extractAttributeFilters(array $data): array
    {
        $attributeFilters = [];

        foreach ($data as $key => $value) {
            if (str_starts_with($key, 'attribute_')) {
                $filterName = str_replace("attribute_", '', $key);
                $attributeFilters[$filterName] = $value;
            }
        }

        return $attributeFilters;
    }

    /**
     * Add sorting to the pipeline.
     */
    private function addSorting(array $data): void
    {
        $sortParam = $data['sort'] ?? 'newest';

        switch ($sortParam) {
            case 'newest':
                $this->addNewestSort();
                break;
            case 'cheapest':
                $this->addCheapestSort();
                break;
            case 'most_expensive':
                $this->addMostExpensiveSort();
                break;
            case 'most_sales':
                $this->addMostSalesSort();
                break;
            case 'most_popular':
                $this->addMostPopularSort();
                break;
            default:
                $this->addNewestSort();
        }
    }

    /**
     * Sort by creation date (newest first).
     */
    private function addNewestSort(): void
    {
        $this->aggregationPipeline[] = [
            '$sort' => ['created_at' => -1]
        ];
    }

    /**
     * Sort by price (cheapest first).
     */
    private function addCheapestSort(): void
    {
        $this->aggregationPipeline[] = [
            '$sort' => ['min_price' => 1]
        ];
    }

    /**
     * Sort by price (most expensive first).
     */
    private function addMostExpensiveSort(): void
    {
        $this->aggregationPipeline[] = [
            '$sort' => ['max_price' => -1]
        ];
    }

    /**
     * Sort by total sales count.
     */
    private function addMostSalesSort(): void
    {
        $this->aggregationPipeline[] = [
            '$addFields' => [
                'total_sales' => [
                    '$sum' => [
                        '$map' => [
                            'input' => [
                                '$filter' => [
                                    'input' => '$invoice_links',
                                    'as' => 'link',
                                    'cond' => [
                                        '$in' => [
                                            '$$link.invoice_object_id',
                                            [
                                                '$map' => [
                                                    'input' => [
                                                        '$filter' => [
                                                            'input' => '$invoices',
                                                            'as' => 'inv',
                                                            'cond' => ['$eq' => ['$$inv.status', 'paid']]
                                                        ]
                                                    ],
                                                    'as' => 'invPaid',
                                                    'in' => '$$invPaid.id'
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            'as' => 'paidLink',
                            'in' => '$$paidLink.quantity'
                        ]
                    ]
                ]
            ]
        ];

        $this->aggregationPipeline[] = [
            '$sort' => ['total_sales' => -1]
        ];
    }

    /**
     * Sort by average rating (most popular first).
     */
    private function addMostPopularSort(): void
    {
        $this->aggregationPipeline[] = [
            '$sort' => ['average_rating' => -1]
        ];
    }

    /**
     * Add pagination using MongoDB facet.
     */
    private function addPaginationFacet(array $data): void
    {
        $limit = $data['limit'] ?? 15;
        $page = $data['page'] ?? 1;
        $skip = $limit * ($page - 1);

        $this->aggregationPipeline[] = [
            '$facet' => [
                'data' => [
                    ['$skip' => $skip],
                    ['$limit' => (int) $limit]
                ],
                'count' => [
                    ['$count' => 'total']
                ]
            ]
        ];
    }

    /**
     * Calculate price statistics for the current query.
     */
    private function calculatePriceStatistics(): array
    {
        // Create a separate pipeline without pagination for price stats
        $statsPipeline = $this->aggregationPipeline;

        // Remove the facet stage if it exists
        $statsPipeline = array_filter($statsPipeline, function ($stage) {
            return !isset($stage['$facet']);
        });

        // Add group stage for price statistics
        $statsPipeline[] = [
            '$group' => [
                'id' => null,
                'min_price' => ['$min' => '$min_price'],
                'max_price' => ['$max' => '$max_price']
            ]
        ];

        $result = Product::raw(function ($collection) use ($statsPipeline) {
            return $collection->aggregate($statsPipeline);
        });

        if ($result && count($result) > 0) {
            return [
                'min_price' => $result[0]['min_price'] ?? 0,
                'max_price' => $result[0]['max_price'] ?? 0
            ];
        }

        return ['min_price' => 0, 'max_price' => 0];
    }

    /**
     * Create Laravel paginator from results.
     */
    private function createPaginator(array $products, int $totalCount, array $data): LengthAwarePaginator
    {
        $limit = $data['limit'] ?? 15;
        $page = $data['page'] ?? 1;

        return new LengthAwarePaginator(
            $products,
            $totalCount,
            $limit,
            $page
        );
    }

    /**
     * Get all category IDs including children recursively.
     */
    private function getAllCategoryIds(Category $category): array
    {
        $categoryIds = [(string) $category->id];

        $children = $category->children()->get();
        foreach ($children as $child) {
            $categoryIds = array_merge($categoryIds, $this->getAllCategoryIds($child));
        }

        return $categoryIds;
    }
}
