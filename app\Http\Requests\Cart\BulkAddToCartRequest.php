<?php

namespace App\Http\Requests\Cart;

use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

/**
 * Bulk Add To Cart Request
 *
 * Validates the request data for adding or updating multiple items in the shopping cart at once.
 * If a product already exists in the cart, its quantity will be set to the given value (not added).
 * If a product doesn't exist in the cart, it will be added with the given quantity.
 */
class BulkAddToCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can add items to cart
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'items' => ['required', 'array', 'min:1'],
            'items.*.variant_id' => [
                'required',
                'integer',
                'exists:product_variants,id'
            ],
            'items.*.quantity' => [
                'nullable',
                'integer',
                'min:1',
            ],
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $items = $this->input('items', []);

            foreach ($items as $index => $item) {
                $variantId = $item['variant_id'] ?? null;
                $quantity = $item['quantity'] ?? 1;

                if (!$variantId) {
                    continue;
                }

                // Find the product variant
                $variant = ProductVariant::find($variantId);

                // Validate that the variant exists
                if (!$variant) {
                    $validator->errors()->add("items.{$index}.variant_id", __('messages.cart.variant_not_found'));
                    continue;
                }

                // Find the parent product
                $product = Product::find($variant->product_id);

                // Validate that the product exists
                if (!$product) {
                    $validator->errors()->add("items.{$index}.variant_id", __('messages.cart.product_not_found'));
                    continue;
                }

                // Check if there's enough stock for the requested quantity
                if ($variant->getCurrentQuantityAttribute() < $quantity) {
                    $validator->errors()->add("items.{$index}.quantity", __('messages.cart.insufficient_stock'));
                    continue;
                }

                // If item already exists in cart, we don't need to check the sum of quantities
                // since we're replacing the quantity, not adding to it
                // We only need to check if the new quantity exceeds available stock
                if ($variant->getCurrentQuantityAttribute() < $quantity) {
                    $validator->errors()->add("items.{$index}.quantity", __('messages.cart.insufficient_stock'));
                    continue;
                }
            }
        });
    }


    protected function prepareForValidation()
    {
        if ($this->has('items')) {
            $items = collect($this->input('items'))->map(function ($item, $index) {
                if (!is_numeric($item['variant_id'])) {
                    throw ValidationException::withMessages([
                        "items.$index.variant_id" => [
                            __("validation.string", [
                                'attribute' => __(
                                    "validation.attributes.variant_id"
                                )
                            ])
                        ],
                    ]);
                }
                $item['variant_id'] = (int) $item['variant_id'];

                $item['variant_id'] = is_numeric($item['variant_id']) ? (int) $item['variant_id'] : null;
                return $item;
            })->toArray();

            $this->merge(['items' => $items]);
        }
    }
    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'items' => 'محصولات',
            'items.*.variant_id' => 'شناسه نوع محصول',
            'items.*.quantity' => 'تعداد',
        ];
    }
}
