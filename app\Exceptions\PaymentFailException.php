<?php

namespace App\Exceptions;

use Symfony\Component\HttpFoundation\Response;

/**
 * Payment Fail Exception
 *
 * Used for errors that occur during the payment Fail ,
 */
class PaymentFailException extends BusinessException
{
    /**
     * Create a new payment preprocess exception instance.
     *
     * @param string $message The error message
     * @param array $errorData Additional error data
     * @param int $statusCode The HTTP status code
     * @param \Throwable|null $previous The previous exception
     * @return void
     */
    public function __construct(
        string $message = 'پرداخت ناموفق',
        array $errorData = [],
        int $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $errorData, $statusCode, $previous);
    }
}
