<?php

namespace App\Http\Controllers\Api\V1\Private\Shopping;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Cart\AddToCart;
use App\Services\Actions\Cart\RemoveCartItem;
use App\Services\Actions\Cart\GetCart;
use App\Http\Requests\Cart\AddToCartRequest;
use App\Http\Requests\Cart\RemoveCartItemRequest;
use App\Http\Requests\Cart\GetCartRequest;
use App\Http\Resources\Shopping\CartResource;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing shopping cart operations
 *
 * @group Shopping Cart
 * @authenticated
 */
class CartController extends BaseController
{
    /**
     * Get the authenticated user's shopping cart
     *
     * @param GetCartRequest $request The validated request
     * @param GetCart $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetCartRequest $request, GetCart $action)
    {
        $res = $action->handle($request->validated());

        return $this->sendResponse(
            new CartResource($res),
            __('messages.cart.cart_retrieved')
        );
    }

    /**
     * Add a product variant to the shopping cart
     *
     * @param AddToCartRequest $request The validated request
     * @param AddToCart $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(AddToCartRequest $request, AddToCart $action)
    {
        $cart = $action->handle($request->validated());

        return $this->sendResponse(
            new CartResource($cart),
            __('messages.cart.item_added'),
            Response::HTTP_CREATED
        );
    }

    /**
     * Remove a product variant from the shopping cart
     *
     * @param RemoveCartItemRequest $request The validated request
     * @param string $id The variant ID
     * @param RemoveCartItem $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(RemoveCartItemRequest $request, RemoveCartItem $action)
    {
        $action->handle($request->validated());

        return $this->sendResponse(
            ['success' => true],
            __('messages.cart.item_removed')
        );
    }
}
