<?php

namespace App\Services\Actions\Cart;

use App\Models\Shopping\ShoppingCart;
use App\Traits\Disconunt\AppliesDiscountCode;
use App\Traits\Cart\CalculatesCartPricing;

/**
 * GetCart Action
 *
 * Retrieves the current user's shopping cart with all items.
 * If the cart doesn't exist, it creates an empty one.
 */
class GetCart
{
    use AppliesDiscountCode, CalculatesCartPricing;
    /**
     * Handle retrieving the user's cart.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return array
     */
    public function handle(array $data): array
    {
        $userId = auth()->id();

        $cart = ShoppingCart::where('user_id', $userId)
            ->first();

        if (!$cart) {
            $cart = ShoppingCart::create(['user_id' => $userId]);
        }

        $this->loadCartRelationships($cart);

        $discountAmount = 0;
        $finalTotal = null;
        if (!empty($data['discount_code'])) {
            $items = $cart->items;
            $total = $items->sum(fn($item) => $item->total);
            [$discountAmount, $finalTotal, $discountCode] = $this->applyDiscountToCart($data['discount_code'], $total);
        }

        return $this->formatCartData($cart, $discountAmount, $finalTotal);
    }
}
