<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the invoice_products table in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id'); // FK to invoices.id
            $table->foreignId('product_id'); // Reference to the product
            $table->foreignId('product_variant_id'); // Reference to the product variation
            $table->integer('quantity'); // Quantity of this product in the invoice
            $table->double('price'); // Regular price of the product at the time of purchase
            $table->double('sale_price')->nullable(); // Sale price of the product at the time of purchase (if applicable)
            $table->string('name'); // Snapshot of the product name
            $table->string('image')->nullable(); // Snapshot of the product image URL
            $table->string('sku'); // Snapshot of the product image URL

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_products');
    }
};
