<?php

namespace App\Http\Controllers\Api\V1\Private\User;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Wallet\TopUpWalletRequest;
use App\Http\Resources\User\WalletTransactionResource;
use App\Services\Actions\Wallet\TopUpWallet;

/**
 * Controller for managing wallet top-up operations
 *
 * @group Wallet Management
 * @authenticated
 */
class WalletTopUpController extends BaseController
{
    /**
     * Create a new wallet top-up transaction.
     *
     * Initiates the payment process for adding funds to the user's wallet.
     *
     * @param TopUpWalletRequest $request The validated request
     * @param TopUpWallet $topUpWallet The wallet top-up action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(TopUpWalletRequest $request, TopUpWallet $topUpWallet)
    {
        $transaction = $topUpWallet->handle($request->validated());
        return $this->sendResponse(
            new WalletTransactionResource($transaction['model'], $transaction['gateway_url']),
            __('messages.wallet.funds_added_initiated')
        );
    }
}
