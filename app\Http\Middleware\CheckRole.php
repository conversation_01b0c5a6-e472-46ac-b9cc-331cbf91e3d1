<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

/**
 * Simple middleware to check if the authenticated user has the required role.
 */
class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $role)
    {
        // Check if user is authenticated and has the required role
        if (!auth()->check() || auth()->user()->role != $role) {
            return response()->json([
                'success' => false,
                'message' => __('messages.common.access_denied'),
            ], 403);
        }

        return $next($request);
    }
}
