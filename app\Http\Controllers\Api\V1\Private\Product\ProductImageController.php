<?php

namespace App\Http\Controllers\Api\V1\Private\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\Product\UploadProductImageRequest;
use App\Http\Resources\Product\ProductResource;
use App\Models\Content\Gallery;
use App\Models\Product\Product;
use App\Services\Actions\Product\DeleteProductImage;
use App\Services\Actions\Product\UploadProductImage;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class ProductImageController extends BaseController
{
    /**
     * Upload an image for the specified product.
     *
     * @param \App\Models\Product\Product $product The product to which the image will be uploaded.
     * @param \App\Http\Requests\Product\UploadProductImageRequest $request The request containing the image file and caption.
     * @param \App\Services\Actions\Product\UploadProductImage $action The action that handles the upload logic.
     * @return \Illuminate\Http\JsonResponse The response containing the updated product resource.
     */
    public function store(Product $product, UploadProductImageRequest $request, UploadProductImage $action): JsonResponse
    {
        $data = $action->handle([
            'product' => $product,
            ...$request->validated(),
        ]);

        return $this->sendResponse(
            new ProductResource($data),
            __('messages.product.image_uploaded')
        );
    }

    /**
     * delete given id image
     */
    public function destroy(Product $product, Gallery $image, DeleteProductImage $action): JsonResponse
    {
        $data = $action->handle([
            'product' => $product,
            "image" => $image,
        ]);

        return $this->sendResponse(
            new ProductResource($data),
            __('messages.product.image_uploaded')
        );
    }
}
