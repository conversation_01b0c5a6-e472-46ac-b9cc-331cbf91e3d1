<?php

namespace App\Traits\Cart;

use App\Models\Shopping\ShoppingCart;

/**
 * CalculatesCartPricing Trait
 *
 * This trait provides methods for calculating cart pricing information
 * and formatting cart data for CartResource consumption.
 */
trait CalculatesCartPricing
{
    /**
     * Calculate cart pricing and return formatted data for CartResource.
     *
     * @param ShoppingCart $cart The cart model with loaded relationships
     * @param float $additionalDiscount Additional discount amount (e.g., from discount codes)
     * @param float|null $finalTotal Override total if discount code was applied
     * @return array Formatted cart data with model and pricing information
     */
    protected function formatCartData(ShoppingCart $cart, float $additionalDiscount = 0, ?float $finalTotal = null): array
    {
        // Ensure relationships are loaded
        if (!$cart->relationLoaded('items')) {
            $cart->load('items.productVariant.product', 'items.productVariant.attributes');
        }

        $items = $cart->items;
        $subtotal = $items->sum(fn($item) => $item->subtotal);
        $totalDiscount = $items->sum(fn($item) => $item->discount);
        $total = $items->sum(fn($item) => $item->total);

        return [
            'model' => $cart,
            'subtotal' => $subtotal,
            'total_discount' => $totalDiscount + $additionalDiscount,
            'total' => $finalTotal ?? $total
        ];
    }

    /**
     * Load required relationships for cart operations.
     *
     * @param ShoppingCart $cart
     * @return ShoppingCart
     */
    protected function loadCartRelationships(ShoppingCart $cart): ShoppingCart
    {
        return $cart->load('items.productVariant.product', 'items.productVariant.attributes');
    }
}
