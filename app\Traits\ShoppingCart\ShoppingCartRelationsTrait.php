<?php

namespace App\Traits\ShoppingCart;

use App\Models\Shopping\CartItem;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * ShoppingCart Relations Trait
 *
 * This trait contains all relationship methods for the ShoppingCart model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ShoppingCart
 */
trait ShoppingCartRelationsTrait
{
    /**
     * Get the items in this shopping cart.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany(CartItem::class);
    }
}
