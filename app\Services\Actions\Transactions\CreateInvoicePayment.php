<?php

namespace App\Services\Actions\Transactions;

use App\Enums\PayTypeEnum;
use App\Enums\Product\StatusEnum;
use App\Models\Shopping\Invoice;
use App\Models\Shopping\Transaction;
use App\Models\User\WalletTransaction;
use App\Services\Payment\Contracts\PaymentContract;
use Illuminate\Support\Facades\DB;
use App\Services\Actions\Invoice\FinalizeInvoicePurchase;
use App\Services\Notifications\UserNotificationService;


/**
 * Create Invoice Payment Action
 *
 * Handles the creation of a new payment transaction for an invoice and prepares the payment process.
 */
class CreateInvoicePayment
{
    /**
     * Handle the invoice payment transaction creation process
     *
     * @param array $data The validated data from the request
     * @return array The created transaction model and payment gateway URL
     * @throws \Exception If transaction creation fails
     */
    public function handle(array $data): array
    {

        return DB::transaction(function () use ($data) {
            // Find the invoice
            $invoice = Invoice::where('id', $data["invoice_id"])->first();


            // Get the payment method
            $paymentMethod = $data['payment_method'] ?? 'online';

            // Get the authenticated user
            $user = auth()->user();

            // If payment method is wallet
            if ($paymentMethod == 'wallet') {
                // Check if user has enough balance for full payment
                if ($user->wallet_balance >= $invoice->total) {
                    // Process full wallet payment
                    return $this->processWalletPayment($invoice);
                }
                return $this->processPartialWalletPayment($invoice, $user->wallet_balance);
            }

            // Otherwise, process online payment
            return $this->processOnlinePayment($invoice);
        });
    }

    /**
     * Process online payment through a payment gateway
     *
     * @param Invoice $invoice The invoice to pay
     * @return array The transaction model and payment gateway URL
     */
    private function processOnlinePayment(Invoice $invoice): array
    {

        // Get the payment driver
        $driver = app(PaymentContract::class);

        // Prepare payment data
        $paymentData = [
            "amount" => $invoice->total,
            "description" => "پرداخت فاکتور شماره {$invoice->id}",
        ];

        // Process the payment with the payment gateway
        $payment = $driver->PaymentPreproccess($paymentData);

        // Create the transaction record
        $transaction = Transaction::create([
            'user_id' => $invoice->user_id,
            "status" => PayTypeEnum::PENDING,
            'payable_id' => $invoice->id,
            'payable_type' => get_class($invoice),
            'amount' => $invoice->total,
            'payment_method' => 'online',
            'payment_gateway' => $driver->getPaymentGateWayName(),
            'authority' => $payment['authority'] ?? null,
            'description' => $paymentData['description'],
        ]);

        // Return the transaction model and payment gateway URL
        return [
            'model' => $transaction,
            'gateway_url' => $driver->getPaymentGatewayUrl($payment['authority'])
        ];
    }

    /**
     * Process payment using the user's wallet
     *
     * @param Invoice $invoice The invoice to pay
     * @return array The transaction model
     */
    private function processWalletPayment(Invoice $invoice): array
    {
        // Get the authenticated user
        $user = auth()->user();

        // Create the transaction record
        $transaction = Transaction::create([
            'user_id' => $user->id,
            'payable_id' => $invoice->id,
            'payable_type' => get_class($invoice),
            'status' => PayTypeEnum::PAID,
            'amount' => $invoice->total,
            'payment_method' => 'wallet',
            'description' => __('messages.wallet.invoice_payment_description', ['invoice_id' => $invoice->id]),
            'paid_at' => now(),
        ]);

        // Create a wallet transaction (withdrawal)
        WalletTransaction::create([
            'user_id' => $user->id,
            'type' => WalletTransaction::TYPE_WITHDRAW,
            'amount' => $invoice->total,
            'referenceable_id' => $invoice->id,
            'referenceable_type' => get_class($invoice),
            'description' => __('messages.wallet.invoice_payment_description', ['invoice_id' => $invoice->id]),
        ]);

        app(FinalizeInvoicePurchase::class)->handle($invoice);

        // Update invoice status
        $invoice->update(['status' => PayTypeEnum::PAID]);

        // Send notification to user
        app(UserNotificationService::class)->sendInvoicePaymentSuccess($invoice, $transaction);

        // Return the transaction model
        return [
            'model' => $transaction
        ];
    }

    /**
     * Process partial payment using the user's wallet and online payment
     *
     * @param Invoice $invoice The invoice to pay
     * @param float $walletAmount The amount to pay from wallet
     * @return array The transaction model and payment gateway URL
     */
    private function processPartialWalletPayment(Invoice $invoice, float $walletAmount): array
    {
        $transaction = null;
        $driver = app(PaymentContract::class);
        $payment = null;
        DB::transaction(function () use ($invoice, $walletAmount, &$transaction, $driver, &$payment) {
            // Get the authenticated user
            $user = auth()->user();

            // Calculate the remaining amount to be paid online
            $remainingAmount = $invoice->total - $walletAmount;

            // Get the payment driver

            // Prepare payment data for the remaining amount
            $paymentData = [
                "amount" => $remainingAmount,
                "description" => __('messages.wallet.invoice_remaining_payment_description', ['invoice_id' => $invoice->id]),
            ];

            // Process the payment with the payment gateway
            $payment = $driver->PaymentPreproccess($paymentData);

            // Create the wallet transaction record for the partial payment
            WalletTransaction::create([
                'user_id' => $user->id,
                'type' => WalletTransaction::TYPE_WITHDRAW,
                'amount' => $walletAmount,
                'referenceable_id' => $invoice->id,
                'referenceable_type' => get_class($invoice),
                'description' => __('messages.wallet.invoice_partial_payment_description', ['invoice_id' => $invoice->id]),
            ]);

            // Create the wallet transaction record for the wallet portion
            Transaction::create([
                'user_id' => $user->id,
                'payable_id' => $invoice->id,
                'payable_type' => get_class($invoice),
                'status' => PayTypeEnum::PAID,
                'amount' => $walletAmount,
                'payment_method' => 'wallet',
                'description' => __('messages.wallet.invoice_partial_payment_description', ['invoice_id' => $invoice->id]),
                'paid_at' => now(),
            ]);

            // Create the transaction record for the online payment portion
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'payable_id' => $invoice->id,
                'payable_type' => get_class($invoice),
                'status' => PayTypeEnum::PENDING,
                'amount' => $remainingAmount,
                'payment_method' => 'online',
                'payment_gateway' => $driver->getPaymentGateWayName(),
                'authority' => $payment['authority'] ?? null,
                'description' => $paymentData['description'],
            ]);
        });
        // Return the transaction models and payment gateway URL with partial payment information
        return [
            'model' => $transaction,
            'gateway_url' => $driver->getPaymentGatewayUrl($payment['authority']),

        ];
    }
}
