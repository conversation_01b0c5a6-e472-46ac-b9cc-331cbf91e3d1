<?php

namespace App\Services\Actions\Categories;

use App\Enums\PayTypeEnum;
use App\Enums\Product\ProductSortOption;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Traits\Products\CategoryProductQueryTrait;
use App\Traits\Products\ProductQueryTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Action class to retrieve all products with filtering, sorting, and pagination.
 */
class GetCategoryProductsSql
{
    private const DEFAULT_LIMIT = 15;
    private const DEFAULT_PAGE = 1;
    private const DEFAULT_SORT = "newest";


    /**
     * Handle the product listing request with filters and pagination.
     */

    use ProductQueryTrait, CategoryProductQueryTrait;
    public function handle(array $data): array
    {
        // Find category and get descendant IDs
        $category = $this->findCategory($data['slug']);
        $categoryIds = $category->getAllDescendantIds();
        $sort = ProductSortOption::fromString($data['sort'] ?? self::DEFAULT_SORT);

        // Build base query with category and attribute filters
        $baseQuery = $this->buildBaseProductQuery($data);
        $this->addCategoryFiltersToQuery($baseQuery, $categoryIds);
        $this->addAttributeFiltersToQuery($baseQuery, $data);

        // Build response using shared trait method
        return $this->buildProductResponse($baseQuery, $data, $sort);
    }
}