<?php

namespace App\Traits\Answer;

use App\Models\UserInteraction\Question;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Answer Relations Trait
 *
 * This trait contains all relationship methods for the Answer model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Answer
 */
trait AnswerRelationsTrait
{
    /**
     * Get the question that this answer belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    /**
     * Get the user that created this answer.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
