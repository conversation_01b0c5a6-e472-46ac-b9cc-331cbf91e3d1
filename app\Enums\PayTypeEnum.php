<?php

namespace App\Enums;

use App\Traits\EnumHelpers;
use App\Traits\EnumLabels;

enum PayTypeEnum: int
{
    use EnumLabels;

    use EnumHelpers;

    case PENDING = 0;
    case PAID = 1;
    case REJECTED = 2;


    /**
     * Get the string label for the enum case.
     */
    public function label(): string
    {
        return match ($this) {
            self::REJECTED => 'rejected',
            self::PENDING => 'pending',
            self::PAID => 'paid',
        };
    }

}
