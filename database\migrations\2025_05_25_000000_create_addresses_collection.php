<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the addresses table in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');  // FK to users.id
            $table->string('name');     // Name of the address (e.g., "Home", "Work")
            $table->string('receiver_name');  // Name of the person receiving packages at this address
            $table->string('receiver_phone'); // Phone number of the person receiving packages at this address
            $table->boolean('is_recipient_self')->default(true); // Whether the address is for the user themselves or someone else
            $table->string('province');
            $table->string('city');
            $table->string('zip_code');
            $table->string('address');  // Detailed address
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
