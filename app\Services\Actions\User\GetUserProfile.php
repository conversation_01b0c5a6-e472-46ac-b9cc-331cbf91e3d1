<?php

namespace App\Services\Actions\User;

use App\Models\User\User;

/**
 * GetUserProfile Action
 *
 * Retrieves the authenticated user's profile information.
 */
class GetUserProfile
{
    /**
     * Handle retrieving the user's profile.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return \App\Models\User\User
     */
    public function handle(array $data): User
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Get the user with wallet transactions for balance calculation
        return User::with('walletTransactions')->findOrFail($userId);
    }
}
