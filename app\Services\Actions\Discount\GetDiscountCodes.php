<?php

namespace App\Services\Actions\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Models\Discount\DiscountCode;
use App\Traits\Pagination;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class GetDiscountCodes
{
    use Pagination;

    public function handle(array $data): LengthAwarePaginator
    {
        $query = DiscountCode::withUser()

            // 🔸 1. Active / inactive filter
            ->when(
                isset($data['is_active']),
                fn($q) =>
                $q->where('is_active', (bool) $data['is_active'])
            )

            // 🔸 2. Type enum filter
            ->when(
                isset($data['type']),
                fn($q) =>
                $q->where('type', DiscountTypeEnum::fromString($data['type']))
            )

            // 🔸 3. Search by user phone (join users table if needed)
            ->when(isset($data['phone']), function ($q) use ($data) {
                $q->whereHas(
                    'user',
                    fn($u) =>
                    $u->where('phone', 'like', '%' . $data['phone'] . '%')
                );
            })

            // 🔸 4. Search by title / code
            ->when(
                isset($data['title']),
                fn($q) =>
                $q->where('code', 'like', '%' . $data['title'] . '%')
            )

            // 🔸 5. Date range: created_after / created_before
            ->when(
                isset($data['created_after']),
                fn($q) =>
                $q->where('created_at', '>=', CarbonDate($data['created_after']))
            )
            ->when(
                isset($data['created_before']),
                fn($q) =>
                $q->where('created_at', '<=', CarbonDate($data['created_before']))
            )->latest();

        return $this->applyPagination($query, $data);
    }
}
