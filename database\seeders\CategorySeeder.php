<?php

namespace Database\Seeders;

use App\Enums\Category\CategoryTypeEnum;
use App\Models\Product\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

/**
 * Seeder for creating product categories in the database with nested structure.
 */
class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates categories with Persian names and assigns them types.
     * Also creates nested subcategories and specific required categories.
     */
    public function run(): void
    {

        // Define main categories with their subcategories
        $categoriesStructure = [
            'الکترونیک' => [
                'موبایل' => [
                    'گوشی هوشمند',
                    'لوازم جانبی موبایل',
                    'کاور و محافظ'
                ],
                'کامپیوتر' => [
                    'لپ تاپ',
                    'کامپیوتر رومیزی',
                    'قطعات کامپیوتر'
                ],
                'لوازم صوتی تصویری' => [
                    'هدفون',
                    'اسپیکر',
                    'تلویزیون'
                ]
            ],
            'پوشاک' => [
                'پوشاک مردانه' => [
                    'پیراهن',
                    'شلوار',
                    'کت و شلوار'
                ],
                'پوشاک زنانه' => [
                    'بلوز',
                    'دامن',
                    'مانتو'
                ],
                'پوشاک کودک' => [
                    'لباس نوزاد',
                    'لباس پسرانه',
                    'لباس دخترانه'
                ]
            ],
            'لوازم خانگی' => [
                'آشپزخانه' => [
                    'ظروف پخت و پز',
                    'لوازم برقی آشپزخانه',
                    'سرویس غذاخوری'
                ],
                'تمیزی و نظافت' => [
                    'مواد شوینده',
                    'ابزار نظافت',
                    'جاروبرقی'
                ]
            ],
            'کتاب و لوازم التحریر' => [
                'کتاب' => [
                    'رمان',
                    'کتاب کودک',
                    'کتاب علمی'
                ],
                'لوازم التحریر' => [
                    'خودکار و مداد',
                    'دفتر و کاغذ',
                    'کیف مدرسه'
                ]
            ],
            'ورزشی' => [
                'پوشاک ورزشی' => [
                    'تی شرت ورزشی',
                    'شورت ورزشی',
                    'کفش ورزشی'
                ],
                'تجهیزات ورزشی' => [
                    'دمبل و وزنه',
                    'توپ',
                    'چادر کوهنوردی'
                ]
            ],
            'زیبایی و سلامت' => [
                'آرایشی' => [
                    'لوازم آرایش چشم',
                    'لوازم آرایش لب',
                    'کرم پودر'
                ],
                'بهداشتی' => [
                    'شامپو',
                    'مراقبت پوست',
                    'عطر و ادکلن'
                ]
            ]
        ];

        // Create categories with nested structure
        foreach ($categoriesStructure as $mainCategoryName => $subCategories) {
            $mainSlug = str_replace(' ', '-', $mainCategoryName);



            // Create main category
            $mainCategory = Category::updateOrCreate(
                ['slug' => $mainSlug],
                [
                    'title' => $mainCategoryName,
                    'type' => 0,
                    'slug' => $mainSlug,
                    'parent_id' => null,
                ]
            );

            // Create subcategories
            foreach ($subCategories as $subCategoryName => $childCategories) {
                $subSlug = str_replace(' ', '-', $subCategoryName);

                $subCategory = Category::updateOrCreate(
                    ['slug' => $subSlug],
                    [
                        'title' => $subCategoryName,
                        'type' => 0,
                        'slug' => $subSlug,
                        'parent_id' => $mainCategory->id,
                    ]
                );

                // Create child categories (third level)
                foreach ($childCategories as $childCategoryName) {
                    $childSlug = str_replace(' ', '-', $childCategoryName);

                    Category::updateOrCreate(
                        ['slug' => $childSlug],
                        [
                            'title' => $childCategoryName,
                            'type' => 0,
                            'slug' => $childSlug,
                            'parent_id' => $subCategory->id,
                        ]
                    );
                }
            }
        }

        // Create additional standalone categories
        $standaloneCategories = [
            'اسباب بازی',
            'خودرو',
            'ابزار',
            'سفر'
        ];

        foreach ($standaloneCategories as $title) {
            $slug = str_replace(' ', '-', $title);


            Category::updateOrCreate(
                ['slug' => $slug],
                [
                    'title' => $title,
                    'type' => 0,
                    'slug' => $slug,
                    'parent_id' => null,
                ]
            );
        }

        // Create specific categories needed for other seeders
        $requiredCategories = [
            [
                'title' => 'کالکشن تابستانه',
                'type' => 0,
                'slug' => 'summer-collection'
            ],
            [
                'title' => 'لوازم جانبی',
                'type' => 0,
                'slug' => 'accessories'
            ]
        ];

        foreach ($requiredCategories as $category) {
            Category::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }

        $this->command->info('دسته‌بندی‌ها و زیر دسته‌بندی‌ها با موفقیت ایجاد شدند.');
    }
}
