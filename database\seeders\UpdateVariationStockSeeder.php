<?php

namespace Database\Seeders;

use App\Models\Product\Product;
use Illuminate\Database\Seeder;

class UpdateVariationStockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Retrieve all products with their variants
        $products = Product::with('variants')->get();

        foreach ($products as $product) {
            foreach ($product->variants as $variation) {
                // Get the current quantity from the attribute
                $currentQuantity = $variation->current_quantity;

                // Update the stock field
                $variation->stock = $currentQuantity;
                $variation->save();
            }
        }

        $this->command->info('Variation stock updated successfully.');
    }
}
