<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;

/**
 * Seeder for creating keywords and associating them with products.
 */
class KeywordSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Assigns 2 random keywords to each product from a predefined list.
     */
    public function run(): void
    {
        $keywords = ['eco', 'local', 'fresh', 'organic', 'handmade', 'premium'];

        foreach (\App\Models\Product\Product::all() as $product) {
            collect($keywords)
                ->shuffle()
                ->take(2)
                ->each(function ($word) use ($product) {
                    $product->keywords()->create(['title' => $word]);
                });
        }
    }
}
