<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User\Shop;

/**
 * Seeder for creating shops in the database.
 */
class ShopSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates 5 shops with Persian names.
     */
    public function run(): void
    {
        // Create 5 shops with Persian names
        $shopNames = [
            'فروشگاه دیجیتال ایران',
            'فروشگاه پارس دیجیتال',
            'فروشگاه تهران مارکت',
            'فروشگاه آنلاین پرشیا',
            'فروشگاه اینترنتی ایران کالا'
        ];

        foreach ($shopNames as $shopName) {
            Shop::firstOrCreate(['title' => $shopName]);
        }
    }
}
