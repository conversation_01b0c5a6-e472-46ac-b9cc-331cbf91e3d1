<?php

namespace App\Models\Content;

use Illuminate\Database\Eloquent\Model;

class Keywordable extends Model
{
    
    

    protected $fillable = [
        'keyword_id',
        'keywordable_id',
        'keywordable_type',
    ];

    protected $casts = [
        'keyword_id' => 'string',
        'keywordable_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
