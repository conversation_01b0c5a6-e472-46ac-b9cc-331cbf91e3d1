<?php

namespace App\Services\Actions\Product;

use App\Enums\Product\ProductSortOption;
use App\Traits\Products\ProductQueryTrait;

/**
 * Action class to retrieve all products with filtering, sorting, and pagination.
 */
class GetAllProductsSql
{
    private const DEFAULT_LIMIT = 15;
    private const DEFAULT_PAGE = 1;
    private const DEFAULT_SORT = "newest";
    
    use ProductQueryTrait;


    /**
     * Handle the product listing request with filters and pagination.
     */
    public function handle(array $data): array
    {
        $sort = ProductSortOption::fromString($data['sort'] ?? self::DEFAULT_SORT);

        // Build base query without category/attribute filters
        $baseQuery = $this->buildBaseProductQuery($data);

        // Build response using shared trait method
        return $this->buildProductResponse($baseQuery, $data, $sort);
    }
}