<?php

namespace App\Http\Resources\Product\Favorite;

use App\Http\Resources\Product\ProductListItemResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming product index data into API responses.
 */
class FavoriteProductCollection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Pagination data
     * - Total products count
     * - Min and max prices
     * - Products array with essential information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $products = $this->items();
        return [
            'products' => ProductListItemResource::collection($products ?? []),
            'pagination' => [
                'current_page' => $this->currentPage(),
                'last_page' => $this->lastPage(),
                'limit' => $this->perPage(),
                'total' => $this->total(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
            ],

        ];
    }
}
