<?php

namespace Database\Seeders;

use App\Models\Ticket\Ticket;
use App\Models\Ticket\TicketMessage;
use Illuminate\Database\Seeder;

class TicketSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
    Ticket::factory()
            ->count(50)
            ->create()
            ->each(function (Ticket $ticket) {
                TicketMessage::factory()
                    ->count(fake()->numberBetween(3, 10))
                    ->for($ticket)
                    ->create([
                        // Alternate between customer & agent if we have an assignee
                        'user_id' => fake()->boolean()
                            ? $ticket->user_id
                            : ($ticket->assigned_to_id ?? $ticket->user_id),
                    ]);
            });
    }
}
