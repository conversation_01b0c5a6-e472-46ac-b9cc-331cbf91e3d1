<?php

namespace App\Http\Requests\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Rules\JalaliDateRule;
use Illuminate\Foundation\Http\FormRequest;

class GetDiscountCodesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'is_active' => 'sometimes|boolean',
            'type' => [
                'sometimes',
                DiscountTypeEnum::rule(),
            ],
            'phone' => 'sometimes|string|exists:users,phone',
            'title' => 'sometimes|string',
            'created_after' => [
                "sometimes",
                new JalaliDateRule()
            ],
            'created_before' => [
                "sometimes",
                new JalaliDateRule()
            ],
            'page' => 'sometimes|integer|min:1',
            'limit' => 'sometimes|integer|min:1|max:20'
        ];
    }
}
