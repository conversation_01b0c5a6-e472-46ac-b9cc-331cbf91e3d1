<?php

namespace App\Http\Requests\SellerAccount;

use App\Models\SellerAccounting\SellerAccount;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Delete Seller Account Request
 *
 * Validates the request data for deleting a seller account.
 */
class DeleteSellerAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated sellers can delete accounts
        if (!auth()->check() || !auth()->user()->isSeller()) {
            return false;
        }

        // Get the account from route model binding
        $account = $this->route('account');

        // Check if the account belongs to the authenticated user
        if ($account->user_id !== auth()->id()) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

        ];
    }
}
