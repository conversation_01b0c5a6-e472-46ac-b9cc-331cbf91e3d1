<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the shopping_carts table in MongoDB.
 *
 * This migration creates a table to store shopping cart data for both
 * registered users and guest visitors. Cart items are stored in a separate
 * table with a reference to the cart ID.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     *
     * Creates the shopping_carts table with fields for user identification
     * and timestamps for tracking creation and updates.
     */
    public function up(): void
    {
        Schema::create('shopping_carts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * Removes the shopping_carts table if it exists.
     */
    public function down(): void
    {
        Schema::dropIfExists('shopping_carts');
    }
};
