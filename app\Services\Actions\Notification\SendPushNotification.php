<?php

namespace App\Services\Actions\Notification;

use App\Services\Notifications\NotificationSender;
use App\Models\Notification\Notification;
use Carbon\Carbon;

/**
 * Action class for creating and scheduling push notifications.
 *
 * This class handles the creation of notification records in the database,
 * which will later be processed and sent by a scheduled task.
 */
class SendPushNotification
{
    /**
     * Stores the last error message if an operation fails.
     *
     * @var string|null
     */
    protected $lastError = null;

    /**
     * Create a new notification record in the database.
     *
     * @param array $params Parameters for the notification
     *                      Required: title, body, type, target, time_to_send
     *                      Optional: image, payload
     * @return bool True if the notification was created successfully
     */
    public function handle(array $params): bool
    {
        $rawTime = $params['time_to_send'] ?? null;


        if ($rawTime) {
            $utcTime = Carbon::parse($rawTime, 'Asia/Tehran')->utc();
        }

        $notification = Notification::create([
            'title' => $params['title'],
            'description' => $params['body'],
            'image' => $params['image'] ?? null,
            'payload' => $params['payload'] ?? [],
            'type' => $params['type'],       // 'token' or 'topic'
            'target' => $params['target'],
            'time_to_send' => $utcTime,
            'sent_time' => null,
            'status' => 'pending',
            'error' => null,
        ]);



        return true;
    }

    /**
     * Get the last error that occurred during the operation
     *
     * @return string|null The error message or null if no error occurred
     */
    public function getLastError()
    {
        return $this->lastError;
    }
}
