<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Get All Products Request
 *
 * Validates the request data for retrieving products with filtering, sorting, and pagination.
 */
class GetAllProductsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // This is a public endpoint
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1|max:100',
            'search' => 'string|max:255',
            'min_price' => 'integer|min:0',
            'max_price' => [
                'integer',
                'min:0',
                function ($_, $value, $fail) {
                    if ($this->min_price != null && $value < $this->min_price) {
                        $fail(__('messages.product.max_price_greater_than_min'));
                    }
                }
            ],
            'sort' => 'string|in:newest,cheapest,most_expensive,most_sales,most_popular',
            'in_stock_only' => 'sometimes|in:true,false',
            'has_guarantee_only' => 'sometimes|in:true,false',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'max_price.gte' => __('messages.product.max_price_greater_than_min'),
            'limit.max' => __('messages.product.per_page_limit'),
            'sort.in' => __('messages.product.invalid_sort_option'),
        ];
    }

}
