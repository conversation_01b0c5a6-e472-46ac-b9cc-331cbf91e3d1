<?php

namespace App\Http\Requests\Cart;

use App\Models\Shopping\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Remove Cart Item Request
 *
 * Validates the request data for removing an item from the shopping cart.
 */
class RemoveCartItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can remove items from cart
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'variant_id' => 'nullable|string',

        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $userId = auth()->id();
            $variantId = $this->validated('variant_id');

            // Find the user's cart
            $cart = ShoppingCart::where('user_id', $userId)->first();

            if (!$cart) {
                $validator->errors()->add('cart', __('messages.cart.cart_empty'));
                return;
            }

            // Find the item in the cart by variant_id
            $item = $cart->items()
                ->where('product_variant_id', $variantId)
                ->first();

            if (!$item) {
                $validator->errors()->add('variant_id', __('messages.cart.item_not_found'));
                return;
            }

            // Store cart and item in request for later use
            $this->merge([
                'variant_id' => $variantId,
            ]);
        });
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'variant_id' => $this->route('variant_id'),
        ]);
    }
}
