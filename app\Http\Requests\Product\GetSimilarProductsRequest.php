<?php

namespace App\Http\Requests\Product;

use App\Models\Product\Product;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Get Similar Products Request
 *
 * Validates the request data for retrieving similar products.
 */
class GetSimilarProductsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so anyone can access it
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'slug' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Find the product by slug
                    $product = Product::where('slug', $value)->first();

                    // If product not found, add error
                    if (!$product) {
                        $fail(__('messages.product.not_found'));
                    }
                },
            ],
            'limit' => 'sometimes|integer|min:1|max:20',
        ];
    }

    // Custom validation messages are now handled by lang/fa/validation.php

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Add the product slug from the route parameter to the validated data
        $this->merge([
            'slug' => $this->route('slug'),
        ]);
    }
}
