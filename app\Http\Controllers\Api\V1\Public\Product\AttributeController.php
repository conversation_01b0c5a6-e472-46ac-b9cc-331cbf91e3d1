<?php

namespace App\Http\Controllers\Api\V1\Public\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\GetAttributesRequest;
use App\Http\Resources\Product\AttributeResource;
use App\Services\Actions\Product\GetAttributes;

/**
 * Controller for retrieving product attributes
 *
 * @group Product Attributes
 */
class AttributeController extends BaseController
{
    /**
     * Get all product attributes
     *
     * Returns a list of all product attributes with their values.
     * Can be filtered by title.
     *
     * @param GetAttributesRequest $request The validated request
     * @param GetAttributes $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetAttributesRequest $request, GetAttributes $action)
    {
        $attributes = $action->handle($request->validated());

        return $this->sendResponse(
            AttributeResource::collection($attributes),
            __('messages.attribute.found')
        );
    }
}
