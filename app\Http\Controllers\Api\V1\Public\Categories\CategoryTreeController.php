<?php

namespace App\Http\Controllers\Api\V1\Public\Categories;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\Categories\CategoryTreeResource;
use App\Models\Product\Category;
use App\Services\Actions\Categories\GetCategoryTree;

/**
 * Controller for retrieving category information and related data
 *
 * @group Category Management
 */
class CategoryTreeController extends BaseController
{
    /**
     * Display a listing of the category tree.
     *
     * @param  \App\Services\Actions\Categories\GetCategoryTree $action
     *     The action class responsible for retrieving the category tree.
     *
     * @return \Illuminate\Http\JsonResponse
     *     A JSON response containing the category tree data.
     * @unauthenticated
     */
    public function index(GetCategoryTree $action)
    {
        $categories = $action->handle();

        return $this->sendResponse(
            CategoryTreeResource::collection($categories),
            __('messages.category.found')
        );
    }


    /**
    * Display the subtree of a specific category.
    *
    * @param \App\Models\Product\Category $category
    *     The category model instance for which to retrieve the tree.
    * @param \App\Services\Actions\Categories\GetCategoryTree $action
    *     The action class responsible for retrieving the category subtree.
    *
    * @return \Illuminate\Http\JsonResponse
    *     A JSON response containing the category subtree data.
    
        * @unauthenticated
        */
    public function show(Category $category, GetCategoryTree $action)
    {
        $categories = $action->handle($category);
        return $this->sendResponse(
            CategoryTreeResource::collection($categories),
            __('messages.category.found')
        );
    }
}
