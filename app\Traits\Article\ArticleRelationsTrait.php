<?php

namespace App\Traits\Article;

use App\Models\UserInteraction\Comment;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Article Relations Trait
 *
 * This trait contains all relationship methods for the Article model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Article
 */
trait ArticleRelationsTrait
{
    /**
     * Get the parent model that this article belongs to.
     * This can be any model that uses the morphMany relationship with articles.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function articleable(): MorphTo
    {
        return $this->morphTo();
    }

    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }
}
