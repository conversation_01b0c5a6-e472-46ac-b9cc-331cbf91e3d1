<?php

namespace App\Traits\Products;

use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Builder;
use Symfony\Component\Config\Definition\Exception\Exception;
/**
 * Trait for category-specific functionality
 */
trait CategoryProductQueryTrait
{
    /**
     * Find category by slug
     */
    protected function findCategory(string $slug): \App\Models\Product\Category
    {
        $category = Category::where('slug', $slug)->first();

        if (!$category) {
            throw new Exception("Category not found: {$slug}");
        }

        return $category;
    }

    /**
     * Extract attribute filters from request data
     */
    protected function extractAttributeFilters(array $data): array
    {
        $attributeFilters = [];

        foreach ($data as $key => $value) {
            if (str_starts_with($key, 'attribute_')) {
                $filterName = str_replace('attribute_', '', $key);
                $attributeFilters[$filterName] = $value;
            }
        }

        return $attributeFilters;
    }

    /**
     * Add attribute filters to query
     */
    protected function addAttributeFiltersToQuery(Builder $query, array $data): void
    {
        $attributeFilters = $this->extractAttributeFilters($data);

        foreach ($attributeFilters as $attributeType => $attributeValues) {
            if (!is_array($attributeValues)) {
                $attributeValues = [$attributeValues];
            }

            $query->whereHas('variants.attributes', function ($q) use ($attributeType, $attributeValues) {
                $q->where('attribute_type', $attributeType)
                    ->whereIn('attribute_value', $attributeValues);
            });
        }
    }

    /**
     * Add category filters to base query
     */
    protected function addCategoryFiltersToQuery(Builder $query, array $categoryIds): void
    {
        $query->whereIn('category_id', $categoryIds);
    }
}