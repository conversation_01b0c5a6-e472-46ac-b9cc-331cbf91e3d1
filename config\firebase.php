<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Firebase Project ID
    |--------------------------------------------------------------------------
    |
    | This value is the project ID of your Firebase project.
    |
    */
    'project_id' => env('FIREBASE_PROJECT_ID', null),

    /*
    |--------------------------------------------------------------------------
    | Firebase Client Email
    |--------------------------------------------------------------------------
    |
    | This value is the client email from your Firebase service account.
    |
    */
    'client_email' => env('FIREBASE_CLIENT_EMAIL', null),

    /*
    |--------------------------------------------------------------------------
    | Firebase Private Key
    |--------------------------------------------------------------------------
    |
    | This value is the private key from your Firebase service account.
    | Note: In your .env file, you may need to add quotes and escape newlines.
    | Example: FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nXXXX...\n-----END PRIVATE KEY-----\n"
    |
    */
    'private_key' => env('FIREBASE_PRIVATE_KEY', null),

    /*
    |--------------------------------------------------------------------------
    | Firebase Database URL
    |--------------------------------------------------------------------------
    |
    | This value is the URL of your Firebase Realtime Database.
    |
    */
    'database_url' => env('FIREBASE_DATABASE_URL', null),

    /*
    |--------------------------------------------------------------------------
    | Firebase Storage Bucket
    |--------------------------------------------------------------------------
    |
    | This value is the default storage bucket of your Firebase project.
    |
    */
    'storage_bucket' => env('FIREBASE_STORAGE_BUCKET', null),

    /*
    |--------------------------------------------------------------------------
    | Firebase Server Key
    |--------------------------------------------------------------------------
    |
    | This value is the server key used for legacy FCM API.
    |
    */
    'server_key' => env('FIREBASE_SERVER_KEY', null),

    /*
    |--------------------------------------------------------------------------
    | Firebase Microservice URL
    |--------------------------------------------------------------------------
    |
    | This value is the URL of the Node.js microservice that handles Firebase
    | topic subscriptions and other operations not directly supported by the
    | Firebase Admin SDK for PHP.
    |
    */
    'mircoservice_url' => env('NODE_PUSH_URL', 'http://localhost:3000'),

    /*
    |--------------------------------------------------------------------------
    | Firebase Subscribe Endpoint
    |--------------------------------------------------------------------------
    |
    | This value is the endpoint path for subscribing tokens to topics
    | on the Firebase microservice.
    |
    */
    'subscribe_endpoint' => env('SUBSCRIBE_ENDPOINT', 'subscribe'),
];
