<?php

namespace App\Exceptions;

use Exception;
use Symfony\Component\HttpFoundation\Response;

/**
 * Business Exception
 * 
 * Used for business logic errors that should be reported to the user
 * with a specific message and HTTP status code.
 */
class BusinessException extends Exception
{
    /**
     * The HTTP status code to return
     *
     * @var int
     */
    protected $statusCode = Response::HTTP_BAD_REQUEST;

    /**
     * The error data to include in the response
     *
     * @var array
     */
    protected $errorData = [];

    /**
     * Create a new business exception instance.
     *
     * @param string $message The error message
     * @param array $errorData Additional error data
     * @param int $statusCode The HTTP status code
     * @param \Throwable|null $previous The previous exception
     * @return void
     */
    public function __construct(
        string $message = 'Business rule violation',
        array $errorData = [],
        int $statusCode = Response::HTTP_BAD_REQUEST,
        \Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        
        $this->statusCode = $statusCode;
        $this->errorData = $errorData;
    }

    /**
     * Get the HTTP status code.
     *
     * @return int
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the error data.
     *
     * @return array
     */
    public function getErrorData(): array
    {
        return $this->errorData;
    }
}
