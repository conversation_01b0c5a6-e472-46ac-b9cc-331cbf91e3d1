<?php

namespace App\Traits;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

trait Pagination
{
    private const DEFAULT_LIMIT = 15;
    private const DEFAULT_PAGE = 1;

    private function applyPagination(Builder $query, $data): LengthAwarePaginator
    {
        $page = $data['page'] ?? self::DEFAULT_PAGE;
        $limit = $data['limit'] ?? self::DEFAULT_LIMIT;
        return $query->paginate($limit, ['*'], 'page', $page);
    }
}
