<?php

namespace App\Http\Requests\Guide;

use App\Models\Product\Product;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Get Product Guides Request
 *
 * Validates the request data for retrieving guides related to a product.
 */
class GetProductGuidesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so anyone can access it
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '' => [
                'required',
                'string',
                function ($_, $value, $fail) {
                    $product = Product::where('slug', $value)->first();
                    if (!$product) {
                        $fail(__('messages.product.not_found'));
                    }
                },
            ],
        ];
    }

    // Custom validation messages are now handled by lang/fa/validation.php

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Add the product slug from the route parameter to the validated data
        $this->merge([
            '' => $this->route('slug'),
        ]);
    }
}
