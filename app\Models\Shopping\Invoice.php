<?php

namespace App\Models\Shopping;

use App\Enums\InvoiceDeliveryStatusEnum;
use App\Enums\PayTypeEnum;
use App\Enums\Product\StatusEnum;
use App\Traits\Invoice\InvoiceRelationsTrait;
use App\Traits\Invoice\InvoiceAttributesTrait;
use App\Traits\Models\Helpers\ShamsiCraetedDate;
use Illuminate\Database\Eloquent\Model;

/**
 * Invoice Model
 *
 * Represents an invoice in the system that belongs to a user.
 *
 * @property string $id The unique identifier for the invoice
 * @property string $user_id The ID of the user who owns this invoice
 * @property string $status The status of the invoice (pending, paid, rejected)
 * @property \Carbon\Carbon $created_at When the invoice was created
 * @property \Carbon\Carbon $updated_at When the invoice was last updated
 * @property-read \App\Models\User $user The user who owns this invoice
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Shopping\InvoiceProduct[] $products The products in this invoice
 */
class Invoice extends Model
{
    use InvoiceRelationsTrait, ShamsiCraetedDate;



    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'status',
        'invoice_number',
        'address',
        'receiver_name',
        'receiver_phone',
        'province',
        'city',
        'zip_code',
        'latitude',
        'longitude',
        'subtotal',
        'discount_amount',
        'total',
        'delivery_status',
        'discount_code_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => PayTypeEnum::class,
        'delivery_status' => InvoiceDeliveryStatusEnum::class,
    ];



}
