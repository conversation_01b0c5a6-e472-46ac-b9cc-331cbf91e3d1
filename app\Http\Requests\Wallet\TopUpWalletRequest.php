<?php

namespace App\Http\Requests\Wallet;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Top Up Wallet Request
 *
 * Validates the request data for adding funds to a user's wallet.
 */
class TopUpWalletRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => [
                'required',
                'numeric',
                'min:100000', // Minimum amount (e.g., 10000 Toman)
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'amount.required' => __('messages.wallet.amount_required'),
            'amount.numeric' => __('messages.wallet.amount_numeric'),
            'amount.min' => __('messages.wallet.amount_min'),
        ];
    }
}
