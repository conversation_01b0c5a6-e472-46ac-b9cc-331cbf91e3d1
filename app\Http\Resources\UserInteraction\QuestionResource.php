<?php

namespace App\Http\Resources\UserInteraction;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\UserInteraction\AnswerResource;

/**
 * Resource class for transforming Question models into API responses.
 *
 * Provides a representation of a question with its body, user information, and answers.
 */
class QuestionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Question ID
     * - Question body
     * - Creation date
     * - User information 
     * - Answers (if any)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->load('user');
        $data = [
            'id' => (string) $this->id,
            'body' => $this->body,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            "user" => [
                'id' => (string) $this->user->id,
                'name' => $this->user->full_name ?? 'کاربر ناشناس',
            ]
        ];


        // Add answers if the answers relationship is loaded
        if ($this->relationLoaded('answers')) {
            $data['answers'] = AnswerResource::collection($this->answers);
        }

        return $data;
    }
}
