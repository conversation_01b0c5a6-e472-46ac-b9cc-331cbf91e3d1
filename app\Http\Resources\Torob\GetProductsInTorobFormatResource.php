<?php

namespace App\Http\Resources\Torob;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GetProductsInTorobFormatResource extends JsonResource
{
    /**
     * Transform the resource into a Torob-compatible response.
     */
    public function toArray(Request $request): array
    {
        return [
            'api_version' => 'torob_api_v3',
            'current_page' => $this->currentPage(),
            'total' => $this->total(),
            'max_pages' => $this->lastPage(),
            'products' => TorobProduct::collection($this->items()),
        ];
    }
}
