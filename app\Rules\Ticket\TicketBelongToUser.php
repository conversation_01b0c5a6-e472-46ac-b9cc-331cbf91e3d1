<?php

namespace App\Rules\Ticket;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class TicketBelongToUser implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = auth()->user();

        if (!$user) {
            $fail(__('messages.ticket.not_belong'));
        }
        // $ticket = 
    }
}
