<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming verified Transaction models into API responses.
 *
 * Provides a detailed representation of a verified transaction with all payment details.
 */
class VerifiedTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes all transaction details including payment verification data.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "payable_id" => $this->payable_id ?? null,
            "payable_type" => $this->payable_type ?? null,
            'id' => $this->id,
            'user_id' => $this->user_id,
            'status' => $this->status->label(),
            'track_code' => $this->track_code,
            'amount' => $this->amount,
            'payment_method' => $this->payment_method,
            'payment_gateway' => $this->payment_gateway,
            'authority' => $this->authority,
            'ref_id' => $this->ref_id,
            'paid_at' => $this->shamsiPaidAt,
            'source' => $this->source,
            'terminal_id' => $this->terminal_id,
            'card_number' => $this->card_number,
            'card_hash' => $this->card_hash,
            'fee' => $this->fee,
            'fee_type' => $this->fee_type,
            'shaparak_fee' => $this->shaparak_fee,
            'order_id' => $this->order_id,
            'wages' => $this->wages,
            'code' => $this->code,
            'created_at' => $this->shamsiCreatedAt,
        ];
    }
}
