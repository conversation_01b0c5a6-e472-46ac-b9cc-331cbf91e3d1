<?php

namespace App\Http\Requests\User;

use App\Enums\User\UserNotificationStatusEnum;
use App\Enums\User\UserNotificationTypeEnum;
use Illuminate\Foundation\Http\FormRequest;

class GetUserNotificationsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'limit' => 'sometimes|int',
            'page' => 'sometimes|int',
            'read' => [
                'sometimes',
                UserNotificationStatusEnum::rule(),
            ],
            'type' => [
                'sometimes',
                UserNotificationTypeEnum::rule(),
            ]
        ];
    }
}
