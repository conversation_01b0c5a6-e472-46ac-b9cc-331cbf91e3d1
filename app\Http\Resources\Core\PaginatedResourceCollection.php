<?php

namespace App\Http\Resources\Core;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class PaginatedResourceCollection extends ResourceCollection
{
    protected string $resourceKey;
    protected ?string $resourceClass = null;

    public function __construct($resource, string $resourceKey = 'data', ?string $resourceClass = null)
    {
        parent::__construct($resource);
        $this->resourceKey = $resourceKey;
        $this->resourceClass = $resourceClass;
    }

    public function toArray(Request $request): array
    {
        $items = $this->resourceClass
            ? $this->resourceClass::collection($this->collection)
            : $this->collection;

        return [
            $this->resourceKey => $items,
            'pagination' => [
                'current_page' => $this->currentPage(),
                'last_page' => $this->lastPage(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
            ],
        ];
    }
}
