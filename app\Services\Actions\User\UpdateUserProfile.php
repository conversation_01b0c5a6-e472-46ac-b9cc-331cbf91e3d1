<?php

namespace App\Services\Actions\User;

use App\Models\User\User;
use Storage;


class UpdateUserProfile
{

    public function handle(array $data): User
    {

        $user = auth()->user();
        $profileImage = $user->profileImage()->first();
        // $user->update(['shamsi_birth_date' => $data['shamsi_birth_date']]);

        $user->update($data);
        if (isset($data['profile_image'])) {
            /** @var \Illuminate\Http\UploadedFile $file */
            $file = $data['profile_image'];

            if ($profileImage) {
                Storage::disk(name: 'profile')->delete($profileImage->image_path);
                $user->profileImage()->delete();
            }
            $path = $file->store("{$user->id}", 'profile');

            $user->profileImage()->create([
                "image_path" => $path,
            ]);
        }

        return $user;
    }
}
