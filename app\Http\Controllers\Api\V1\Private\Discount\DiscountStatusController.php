<?php
namespace App\Http\Controllers\Api\V1\Private\Discount;


use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Discount\UpdateDiscountCodeStatusRequest;
use App\Http\Resources\Discount\DiscountResource;
use App\Services\Actions\Discount\UpdateDiscountStatus;
use Symfony\Component\HttpFoundation\JsonResponse;

class DiscountStatusController extends BaseController
{
    /**
     * updates discount code status
     * @param \App\Http\Requests\Discount\UpdateDiscountCodeStatusRequest $request
     * @param \App\Services\Actions\Discount\UpdateDiscountStatus $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(UpdateDiscountCodeStatusRequest $request, UpdateDiscountStatus $action): JsonResponse
    {
        $discountCode = $action->handle($request->validated());
        return $this->sendResponse(
            new DiscountResource(
                $discountCode
            ),
        );
    }
}
