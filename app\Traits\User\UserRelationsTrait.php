<?php

namespace App\Traits\User;

use App\Models\Content\Gallery;
use App\Models\Product\Product;
use App\Models\SellerAccounting\SellerAccount;
use App\Models\User\Address;
use App\Models\Shopping\Invoice;
use App\Models\User\Shop;
use App\Models\User\User;
use App\Models\Shopping\ShoppingCart;
use App\Models\Shopping\Transaction;
use App\Models\User\UserNotification;
use App\Models\User\WalletTransaction;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * User Relations Trait
 *
 * This trait contains all relationship methods for the User model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\User
 */
trait UserRelationsTrait
{
    /**
     * Get the shops that this user belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function shops()
    {
        return $this->belongsToMany(Shop::class);
    }

    /**
     * Get the invoices that belong to this user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the addresses that belong to this user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    /**
     * Get the ShoppingCart that belong to this user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function cart(): HasOne
    {
        return $this->hasOne(ShoppingCart::class);
    }

    /**
     * Get the wallet transactions that belong to this user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get the payment transactions associated with this user (for wallet top-ups).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'payable');
    }

    /**
     * Get the profile image
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function profileImage(): MorphMany
    {
        return $this->morphMany(Gallery::class, 'imageable');
    }
    /**
     * Summary of favoriteProducts
     * @return BelongsToMany<Product, User, \Illuminate\Database\Eloquent\Relations\Pivot>
     */
    public function favoriteProducts()
    {
        return $this->belongsToMany(Product::class, 'product_favorites')
            ->withTimestamps();
    }
    /**
     * Get all of the notifications for the UserRelationsTrait
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userNotifications(): HasMany
    {
        return $this->hasMany(UserNotification::class);
    }
    /**
     *  Get all of the account for the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function accounts(): HasMany
    {
        return $this->hasMany(SellerAccount::class);
    }
}
