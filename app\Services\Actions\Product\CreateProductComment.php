<?php

namespace App\Services\Actions\Product;

use App\Enums\PayTypeEnum;
use App\Enums\Product\StatusEnum;
use App\Models\Product\Product;
use App\Models\UserInteraction\Comment;

class CreateProductComment
{
    public function handle(array $data): Comment
    {

        $product = Product::find($data['id']);

        $commentData = $data;
        $user = auth()->user();
        $hasBought = false;
        if ($user)
            $hasBought = $user->invoices()->where("status", PayTypeEnum::PAID)
                ->whereHas('products', function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                })->exists();

        $commentData['user_id'] = auth()->id();

        $commentData['has_bought'] = $hasBought;

        if (isset($data['reply_to']))
            $commentData['parent_id'] = $data['reply_to'];

        $commentData['status'] = StatusEnum::PENDING;

        $comment = $product->comments()->create($commentData);
        return $comment;
    }


}
