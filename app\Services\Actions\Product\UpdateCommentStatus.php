<?php

namespace App\Services\Actions\Product;

use App\Enums\Product\StatusEnum;
use App\Models\UserInteraction\Comment;

class UpdateCommentStatus
{

    /**
     * Handle the update of a product comment's status.
     *
     * Finds the comment by ID, updates its status, and saves the changes.
     *
     * @param array<string, mixed> $data An associative array containing:
     *                                   - 'comment_id': int|string, the ID of the comment to update.
     *                                   - 'status': string|int, the new status to set.
     * @return Comment The updated comment model instance.
     */
    public function handle(array $data): Comment
    {
        $comment = Comment::find($data["comment_id"]);
        $status = StatusEnum::fromString($data["status"]);
        $comment->status = $status;
        $comment->save();
        return $comment;
    }

}
