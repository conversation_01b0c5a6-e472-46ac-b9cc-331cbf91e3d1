<?php

namespace App\Http\Controllers\Api\V1\Public\Notification;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Notification\StoreClientFirebaseToken;
use App\Http\Resources\Notification\FirebaseTokenResource;
use App\Http\Requests\Notification\StoreClientTokenRequest;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing client device Firebase tokens
 *
 * @group Firebase Token Management
 */
class ClientTokenController extends BaseController
{
    /**
     * Store a new client device Firebase token
     *
     * @param StoreClientTokenRequest $request The validated request
     * @param StoreClientFirebaseToken $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function store(StoreClientTokenRequest $request, StoreClientFirebaseToken $action)
    {
        $firebaseToken = $action->handle($request->validated());

        return $this->sendResponse(
            new FirebaseTokenResource($firebaseToken),
            __('messages.notification.token_stored'),
            Response::HTTP_CREATED
        );
    }
}
