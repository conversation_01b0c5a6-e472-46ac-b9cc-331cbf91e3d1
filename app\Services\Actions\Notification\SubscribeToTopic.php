<?php

namespace App\Services\Actions\Notification;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Action class for subscribing Firebase tokens to topics.
 *
 * This class handles the subscription of FCM tokens to topics,
 * allowing for topic-based notifications to be sent to multiple devices.
 * It communicates with a Firebase microservice to perform the subscription.
 */
class SubscribeToTopic
{
    /**
     * Subscribe a Firebase token to a topic.
     *
     * Makes an HTTP request to the Firebase microservice to subscribe
     * the provided token to the specified topic.
     *
     * @param array $data Input data containing:
     *                    - token: (string) The Firebase token to subscribe
     *                    - topic: (string) The topic to subscribe to
     * @return \Illuminate\Http\Client\Response The HTTP response from the microservice
     */
    public function handle(array $data)
    {
        $url = config('firebase.mircoservice_url');
        $endpoint = config('firebase.subscribe_endpoint');

        $response = Http::withHeaders([
            'x-user' => "shahab",
            "x-key" => "shahab_key"
        ])->post("$url{$endpoint}", [
            'token' => $data['token'],
            'topic' => $data['topic'],
        ]);

        Log::info('[FCM] Response from batchAdd', [
            'response' => $response->body(),
        ]);

        return $response;
    }
}