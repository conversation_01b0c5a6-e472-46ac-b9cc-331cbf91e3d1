<?php

namespace App\Traits\Product;

use Illuminate\Support\Str;

/**
 * Product Attributes Trait
 *
 * This trait contains all attribute methods for the Product model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Product
 */
trait ProductAttributesTrait
{
    /**
     * Boot the trait.
     * Automatically generate slug on creating if not provided.
     *
     * @return void
     */
    protected static function bootProductAttributesTrait(): void
    {
        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = str_replace(" ", "-", $product->title);
            }
        });
    }

    /**
     * Get the product rating attribute based on verified buyer comments.
     *
     * @return float
     */
    public function getRatingAttribute(): float
    {
        $ratedComments = $this->comments()->where('has_bought', true)->get();
        $productRatesCount = $ratedComments->count();

        if ($productRatesCount > 0) {
            return round($ratedComments->avg('rate'), 1);
        }

        return 0;
    }
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
