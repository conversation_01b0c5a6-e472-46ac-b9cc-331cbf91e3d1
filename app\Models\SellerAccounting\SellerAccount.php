<?php

namespace App\Models\SellerAccounting;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SellerAccount extends Model
{
    use HasFactory;

    protected $table = 'seller_accounts';

    protected $fillable = [
        'user_id',
        'bank_id',
        'account_number',
        'card_number',
        'sheba_number',
        'verified',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bank()
    {
        return $this->belongsTo(SupportingBank::class, 'bank_id');
    }
}