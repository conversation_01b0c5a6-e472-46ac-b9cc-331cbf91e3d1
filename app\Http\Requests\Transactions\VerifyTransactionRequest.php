<?php

namespace App\Http\Requests\Transactions;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\Payment\Contracts\PaymentContract;

/**
 * Verify Transaction Request
 *
 * Validates the request data for verifying a transaction after payment gateway callback.
 */
class VerifyTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $driver = app(PaymentContract::class);
        return $driver->getGatewayVerificationRules();
    }
}
