<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\ProductUnit;

class ProductUnitSeeder extends Seeder
{
    public function run(): void
    {
        $units = [
            'عدد',
            'بسته',
            'جعبه',
            'کیلوگرم',
            'گرم',
            'لیتر',
            'میلی‌لیتر',
            'متر',
            'سانتی‌متر',
            'اینچ',
            'دوجین',
            'جفت',
            'رول',
            'ست',
        ];

        foreach ($units as $unit) {
            ProductUnit::firstOrCreate(['name' => $unit]);
        }
    }
}