<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;
use Illuminate\Support\Facades\Storage;

class DeleteProductImage
{
    /**
     * <PERSON>les deleting a product image, removes it from storage,
     * deletes the gallery record, and returns the updated product with its relations.
     *
     * @param array{
     *     product: \App\Models\Product\Product,
     *     image: \App\Models\Content\Gallery,
     * } $data An array containing the product instance and gallery image model.
     *
     * @return \App\Models\Product\Product The product model with loaded relations after image deletion.
     */
    public function handle(array $data): Product
    {
        $product = $data['product'];
        $image = $data['image'];

        // Delete the file from storage
        Storage::disk('product')->delete($image->image_path);

        // Delete the gallery record
        $image->delete();

        return $product;
    }
}