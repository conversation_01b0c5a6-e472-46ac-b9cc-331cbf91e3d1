<?php

namespace App\Http\Resources\Notification;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Firebase topic subscription responses into API responses.
 *
 * This resource simply passes through the message from the Firebase subscription service.
 */
class FirebaseSubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Extracts the message from the HTTP response and returns it.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Simply pass through the raw response data
        return ['message' => $this->resource->json('message')];
    }
}
