<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming DeliveryMethod models into API responses.
 */
class ProductAttributesResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'type' => $this->attribute_type,
            'title' => $this->attribute_title,
            'value' => $this->attribute_value,
            'extra_data' => $this->extra_data,
        ];
    }
}