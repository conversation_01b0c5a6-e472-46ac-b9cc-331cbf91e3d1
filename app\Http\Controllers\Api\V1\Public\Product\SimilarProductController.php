<?php

namespace App\Http\Controllers\Api\V1\Public\Product;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Product\GetSimilarProducts;
use App\Http\Resources\Product\SimilarProductResource;
use App\Http\Requests\Product\GetSimilarProductsRequest;

/**
 * Controller for retrieving similar products
 *
 * @group Product Recommendations
 */
class SimilarProductController extends BaseController
{
    /**
     * Get similar products to the specified product
     *
     * @param GetSimilarProductsRequest $request The validated request
     * @param string $slug The product slug
     * @param GetSimilarProducts $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetSimilarProductsRequest $request, GetSimilarProducts $action)
    {
        // The $slug parameter is used by the route but the actual value is handled in the request
        $similarProducts = $action->handle($request->validated());

        return $this->sendResponse(
            SimilarProductResource::collection($similarProducts),
            __('messages.product.recommendations_found')
        );
    }
}
