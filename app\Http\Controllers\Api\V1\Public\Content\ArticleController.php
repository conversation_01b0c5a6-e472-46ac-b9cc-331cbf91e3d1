<?php

namespace App\Http\Controllers\Api\V1\Public\Content;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Article\GetProductArticles;
use App\Http\Resources\Content\ArticleResource;
use App\Http\Requests\Article\GetProductArticlesRequest;

/**
 * Controller for retrieving article information
 *
 * @group Article Management
 */
class ArticleController extends BaseController
{
    /**
     * Get articles related to a specific product
     *
     * @param GetProductArticlesRequest $request The validated request
     * @param GetProductArticles $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetProductArticlesRequest $request, GetProductArticles $action)
    {
        $articles = $action->handle($request->validated());

        return $this->sendResponse(
            ArticleResource::collection($articles),
            __('messages.article.found')
        );
    }
}
