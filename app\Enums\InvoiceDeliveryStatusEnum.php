<?php

namespace App\Enums;

use App\Traits\EnumHelpers;
use App\Traits\EnumLabels;

enum InvoiceDeliveryStatusEnum: int
{
    use EnumLabels;
    use EnumHelpers;
    case IN_PROGRESS = 0;
    case SENT = 1;
    case DONE = 2;
    case CANCELED = 3;
    public function label(): string
    {
        return match ($this) {
            self::IN_PROGRESS => 'in_progress',
            self::SENT => 'sent',
            self::DONE => 'done',
            self::CANCELED => 'canceled',
        };
    }

    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'in_progress' => self::IN_PROGRESS,
            'sent' => self::SENT,
            'done' => self::DONE,
            'canceled' => self::CANCELED,
            default => self::IN_PROGRESS,
        };
    }
}
