<?php

use App\Enums\PayTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function ($table) {
            $table->id();
            $table->foreignId('user_id');
            $table->string('invoice_number');
            $table->enum('status', PayTypeEnum::values())->default(PayTypeEnum::PENDING);
            $table->string('address');
            $table->string('receiver_name');
            $table->string('receiver_phone');
            $table->string('province');
            $table->string('city');
            $table->string('zip_code');
            $table->string('latitude');
            $table->string('longitude');
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
