<?php

namespace App\Services\Actions\SellerAccounts;

use App\Exceptions\BusinessException;
use App\Models\SellerAccounting\SellerAccount;
use App\Models\SellerAccounting\SupportingBank;

class CreateSellerAccount
{
    /**
     * Gets All Seller accounts
     * @param array $data
     * @return SellerAccount
     */
    public function handle(array $data): SellerAccount
    {
        $user = auth()->user();

        if (!$user->isSeller()) {
            throw new BusinessException(__('messages.user.not_seller'));
        }
        
        if ($user->accounts()->where('verified', false)->count() >= 2) {
            throw new BusinessException(__('messages.seller_accounts.2_not_verified'));
        }
        $bank = SupportingBank::where('uuid', $data['bank_id'])->first();


        $checkExistingAccount = $user->accounts()->where('bank_id', $bank->id)->where('account_number', $data['account_number'])->exists();

        if ($checkExistingAccount) {
            throw new BusinessException(__('messages.seller_accounts.already_exists'));

        }
        $data['bank_id'] = $bank->id;
        $account = $user->accounts()->create($data);
        $account->refresh();
        return $account;
    }
}
