<?php

namespace App\Services\Actions\Ticket;

use App\Enums\Ticket\TicketStatusEnum;
use App\Enums\Ticket\TicketTypeEnum;
use App\Exceptions\BusinessException;
use App\Models\Shopping\Invoice;
use App\Models\Ticket\Ticket;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\Response;

class CreateTicket
{
    /**
     * creates a new ticket for related user
     * @param array $data
     * @param string $creator in:seller,SUPPORT
     * @return Ticket
     */
    public function handle(array $data, $creator): Ticket
    {
        $userId = auth()->id();

        $type = TicketTypeEnum::SELLER;
        if ($creator == 'support') {
            $type = TicketTypeEnum::SUPPORT;
        }

        if (isset($data['invoice_id'])) {
            $invoice = Invoice::where('user_id', $userId)->where('invoice_id', $data['invoice_id'])->first();
            if (!$invoice)
                throw new BusinessException(__('messages.ticket.unauthorized'), [], Response::HTTP_UNAUTHORIZED);
        }
        $ticket = Ticket::create(
            [
                'user_id' => $userId,
                'title' => $data['title'],
                'ticket_status' => TicketStatusEnum::PENDING,
                'type' => $type,
                'department_id' => $data['department_id'],
                'invoice_id' => $data['invoice_id'],
            ]
        );
        return $ticket;
    }
}
