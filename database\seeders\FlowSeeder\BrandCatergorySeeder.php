<?php

namespace Database\Seeders\FlowSeeder;

use App\Models\Product\Category;
use App\Models\Product\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandCatergorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $imageUrls = [
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
        ];
        $category = Category::where('slug', 'گوشی-هوشمند')->first();
        $products = [
            [
                'title' => 'گوشی آیفون ۱۳ پرو مکس',
                'description' => 'گوشی هوشمند اپل با صفحه‌نمایش سوپر رتینا XDR، دوربین سه‌گانه پیشرفته و تراشه A15 Bionic برای عملکرد بی‌نظیر.',
                "brand" => 'اپل'
            ],
            [
                'title' => 'گوشی آیفون ۱۴',
                'description' => 'مدل جدید آیفون با طراحی جذاب، دوربین بهبود یافته و سیستم‌عامل iOS برای تجربه‌ای روان و ایمن.',
                "brand" => 'اپل'

            ],
            [
                'title' => 'گوشی سامسونگ گلکسی S23 اولترا',
                'description' => 'پرچمدار سامسونگ با دوربین ۲۰۰ مگاپیکسلی، قلم S-Pen و صفحه‌نمایش AMOLED با نرخ نوسازی ۱۲۰ هرتز.',
                "brand" => 'سامسونگ'

            ],
            [
                'title' => 'گوشی سامسونگ گلکسی A54',
                'description' => 'گوشی میان‌رده سامسونگ با طراحی زیبا، باتری بادوام و دوربین چهارگانه مناسب برای کارهای روزمره.',
                "brand" => 'سامسونگ'
            ],
            [
                'title' => 'گوشی ال‌جی Velvet',
                'description' => 'گوشی خوش‌ساخت ال‌جی با طراحی باریک، صفحه‌نمایش OLED و قابلیت پشتیبانی از قلم هوشمند.',
                "brand" => 'ال جی'
            ],
            [
                'title' => 'گوشی ال‌جی G8 ThinQ',
                'description' => 'پرچمدار پیشین ال‌جی با عملکرد بالا، قابلیت تشخیص چهره پیشرفته و صدای استریو قدرتمند.',
                "brand" => 'ال جی'

            ],
        ];
        foreach ($products as $product) {
            $createProdcut = Product::updateOrCreate(
                [
                    "slug" => str_replace(' ', '-', $product['title'])
                ],
                [
                    "title" => $product['title'],
                    "description" => $product['description'],
                ]
            );

            foreach (collect($imageUrls)->shuffle()->take(3) as $url) {
                $createProdcut->gallery()->create([
                    'image_url' => $url,
                    'caption' => "تصویر {$createProdcut->title}",
                ]);
            }


            $imploded_titles = $createProdcut->title . "-" . $product['brand'];
            $variant = $createProdcut->variants()->create([
                "sku" => $imploded_titles,
                "price" => generateRandomFakeprice(),
            ]);
            $variant->purchases()->create([
                'quantity' => rand(5, 20), // Positive quantity for purchases
                'price' => rand(100, 2000) / 100,
                'purchased_at' => now()->subDays(rand(0, 30)),
                'invoice_id' => null, // No invoice for purchases
            ]);
            $variant->attributes()->create([
                "attribute_title" => "برند",
                "attribute_value" => $product['brand'],
                "attribute_type" => 'brand'
            ]);


            $createProdcut->category()->attach($category);
        }
    }
}
