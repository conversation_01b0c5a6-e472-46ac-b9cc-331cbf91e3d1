<?php

namespace App\Traits\Attribute;

use App\Models\Product\Attribute;
use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Attribute Relations Trait
 *
 * This trait contains all relationship methods for the Attribute model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Attribute
 */
trait AttributeRelationsTrait
{
    /**
     * Get the parent attribute (for attribute values).
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(Attribute::class, 'parent_id', 'id');
    }

    /**
     * Get the child attributes (values for this attribute title).
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function values()
    {
        return $this->hasMany(Attribute::class, 'parent_id', 'id');
    }

    /**
     * Get the category that this attribute belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
