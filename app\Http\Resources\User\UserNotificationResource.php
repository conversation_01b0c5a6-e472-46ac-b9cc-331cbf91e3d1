<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Resources\Json\JsonResource;

class UserNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'body' => $this->body,
            'is_read' => $this->is_read,
            'data' => $this->data,
            'read_at' => $this->read_at ? $this->shamsiReadAt : null,
            'created_at' => $this->shamsiCreatedAt,
        ];
    }
}
