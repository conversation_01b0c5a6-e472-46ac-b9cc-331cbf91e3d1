<?php

namespace App\Models\User;

use App\Enums\User\UserNotificationTypeEnum;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Model;

class UserNotification extends Model
{

    protected $fillable = [
        'user_id',
        'title',
        'body',
        'is_read',
        'data',
        'read_at',
    ];

    protected $dates = ['read_at', 'created_at', 'updated_at'];

    protected $casts = [
        'data' => 'array',
        'type' => UserNotificationTypeEnum::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function GetShamsiCreatedAtAttribute()
    {
        return shamsiDate($this->created_at);
    }
    public function GetShamsiReadAtAttribute()
    {
        return shamsiDate($this->read_at);
    }
}
