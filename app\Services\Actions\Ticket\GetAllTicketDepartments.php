<?php

namespace App\Services\Actions\Ticket;

use App\Enums\Ticket\TicketStatusEnum;
use App\Enums\Ticket\TicketTypeEnum;
use App\Models\Ticket\Ticket;
use App\Models\Ticket\TicketDepartment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class GetAllTicketDepartments
{
    /**
     * gets all tickets related to user
     * @param array $data
     * @return Collection
     */
    public function handle(): Collection
    {

        return TicketDepartment::all();
    }
}
