<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration to add guarantee fields to invoice_products table
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_products', function (Blueprint $table) {
            $table->foreignId('guarantee_id')->nullable()->after('sku'); // Reference to selected guarantee
            $table->string('guarantee_company_name')->nullable()->after('guarantee_id'); // Snapshot of guarantee company
            $table->integer('guarantee_months')->nullable()->after('guarantee_company_name'); // Snapshot of guarantee duration
            $table->double('guarantee_price')->nullable()->after('guarantee_months'); // Snapshot of guarantee price
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_products', function (Blueprint $table) {
            $table->dropColumn([
                'guarantee_id',
                'guarantee_company_name', 
                'guarantee_months',
                'guarantee_price'
            ]);
        });
    }
};
