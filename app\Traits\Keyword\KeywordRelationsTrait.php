<?php

namespace App\Traits\Keyword;

use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Keyword Relations Trait
 *
 * This trait contains all relationship methods for the Keyword model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Keyword
 */
trait KeywordRelationsTrait
{
    /**
     * Get the parent model that this keyword belongs to.
     * This can be any model that uses the morphMany relationship with keywords.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function keywordable(): MorphTo
    {
        return $this->morphTo();
    }
}
