<?php

namespace App\Providers;

use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Support\ServiceProvider;
use App\Services\Payment\Contracts\PaymentContract;
use App\Services\Payment\Factories\PaymentFactory;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(PaymentContract::class, fn() => PaymentFactory::create());

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure Scramble to document API authentication
        Scramble::configure()
            ->withDocumentTransformers(function (OpenApi $openApi) {
                // Define API key authentication using the Authorization header
                $scheme = SecurityScheme::apiKey('header', 'Authorization');
                $scheme->description = 'API key for authentication. Use format: "Bearer YOUR_API_KEY"';

                // Apply authentication globally - use @unauthenticated to exclude specific routes
                $openApi->secure($scheme);
            });
    }
}
