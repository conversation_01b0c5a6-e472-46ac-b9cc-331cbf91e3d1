<?php

namespace App\Http\Resources\Notification;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming FirebaseToken models into API responses.
 *
 * This resource formats the token data for client consumption, including
 * the token itself and metadata about the device that registered it.
 */
class FirebaseTokenResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - The Firebase token string
     * - Path where the token was registered
     * - IP address of the client
     * - User agent of the client
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'token' => $this->token,
            'path' => $this->path,
            'ip' => $this->ip,
            'agent' => $this->agent,
        ];
    }
}
