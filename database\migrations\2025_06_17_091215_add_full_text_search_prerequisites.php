<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up()
    {
        // Add tsvector columns for full-text search using raw SQL
        DB::statement('ALTER TABLE products ADD COLUMN title_search tsvector');
        DB::statement('ALTER TABLE products ADD COLUMN description_search tsvector');
        DB::statement('ALTER TABLE products ADD COLUMN combined_search tsvector');

        // Create indexes for better performance
        DB::statement('CREATE INDEX products_title_search_idx ON products USING GIN(title_search)');
        DB::statement('CREATE INDEX products_description_search_idx ON products USING GIN(description_search)');
        DB::statement('CREATE INDEX products_combined_search_idx ON products USING GIN(combined_search)');

        // Create triggers to automatically update tsvector columns
        DB::statement("
            CREATE OR REPLACE FUNCTION products_search_trigger() R<PERSON><PERSON>NS trigger AS $$
            BEGIN
                NEW.title_search := to_tsvector('english', COALESCE(NEW.title, ''));
                NEW.description_search := to_tsvector('english', COALESCE(NEW.description, ''));
                NEW.combined_search := to_tsvector('english', 
                    COALESCE(NEW.title, '') || ' ' || COALESCE(NEW.description, '')
                );
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        ");

        DB::statement('
            CREATE TRIGGER products_search_update 
            BEFORE INSERT OR UPDATE ON products 
            FOR EACH ROW EXECUTE FUNCTION products_search_trigger()
        ');

        // Update existing records
        DB::statement("
            UPDATE products SET 
                title_search = to_tsvector('english', COALESCE(title, '')),
                description_search = to_tsvector('english', COALESCE(description, '')),
                combined_search = to_tsvector('english', 
                    COALESCE(title, '') || ' ' || COALESCE(description, '')
                )
        ");
    }

    public function down()
    {
        DB::statement('DROP TRIGGER IF EXISTS products_search_update ON products');
        DB::statement('DROP FUNCTION IF EXISTS products_search_trigger()');

        // Drop tsvector columns using raw SQL
        DB::statement('ALTER TABLE products DROP COLUMN IF EXISTS title_search');
        DB::statement('ALTER TABLE products DROP COLUMN IF EXISTS description_search');
        DB::statement('ALTER TABLE products DROP COLUMN IF EXISTS combined_search');
    }
};
