<?php

namespace App\Http\Controllers\Api\V1\Public\Comment;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\CreateProductCommentRequest;
use App\Http\Resources\UserInteraction\CommentResource;
use App\Services\Actions\Product\CreateProductComment;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Services\Actions\Comment\GetProductComments;
use App\Http\Requests\Comment\GetProductCommentsRequest;
class ProductCommentController extends BaseController
{
    /**
     * Store a newly created product comment in storage.
     *
     * This method handles the request to create a new comment for a product,
     * using the provided validated data, user agent, and IP address.
     *
     * @param CreateProductCommentRequest $request The incoming request containing validated data.
     * @param CreateProductComment $action The action class responsible for handling comment creation.
     * @return JsonResponse A JSON response containing the created comment resource and a success message.
     */
    public function store(CreateProductCommentRequest $request, CreateProductComment $action): JsonResponse
    {
        $comment = $action->handle([
            "user_agent" => $request->userAgent(),
            "ip" => $request->ip(),
            ...$request->validated()
        ]);

        return $this->sendResponse(
            new CommentResource($comment),
            __('messages.comments.created')
        );
    }
    /**
     * Get comments related to a specific product
     *
     * @param GetProductCommentsRequest $request The validated request
     * @param string $slug The product slug
     * @param GetProductComments $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetProductCommentsRequest $request, GetProductComments $action)
    {
        $comments = $action->handle($request->validated());

        return $this->sendResponse(
            CommentResource::collection($comments),
            __('messages.comment.found')
        );
    }
}

