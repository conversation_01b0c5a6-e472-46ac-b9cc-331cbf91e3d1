<?php

namespace Database\Seeders;

use App\Models\SellerAccounting\SupportingBank;
use Illuminate\Database\Seeder;

class SupportingBanksSeeder extends Seeder
{
    public function run(): void
    {
        $banks = [
            ['title' => 'بانک ملی ', 'logo_path' => 'melli.png'],
            ['title' => 'بانک صادرات', 'logo_path' => 'saderat.png'],
            ['title' => 'بانک کشاورزی', 'logo_path' => 'keshavarzi.png'],
            ['title' => 'بانک ملت', 'logo_path' => 'mellat.png'],
            ['title' => 'بانک تجارت', 'logo_path' => 'tejarat.png'],
        ];

        foreach ($banks as $bank) {

            $createdBank = SupportingBank::create([
                'title' => $bank['title']
            ]);

            $createdBank->logo()->create(
                [
                    'image_path' => $bank['logo_path']
                ]
            );

        }
    }
}