<?php

namespace App\Traits\WalletTransaction;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * WalletTransaction Relations Trait
 *
 * This trait contains all relationship methods for the WalletTransaction model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\WalletTransaction
 */
trait WalletTransactionRelationsTrait
{
    /**
     * Get the user that owns this wallet transaction.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent model that this wallet transaction references.
     * This can be any model that the wallet transaction is associated with.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function referenceable(): MorphTo
    {
        return $this->morphTo();
    }
}
