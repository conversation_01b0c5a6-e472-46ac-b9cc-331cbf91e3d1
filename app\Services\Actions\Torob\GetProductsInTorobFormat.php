<?php
namespace App\Services\Actions\Torob;

use App\Enums\TorobRequestSortType;
use App\Enums\TorobRequestTypeEnum;
use App\Models\Product\ProductVariant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Service class for retrieving products in Torob-compatible format.
 * 
 * Handles different types of product requests (by URL, by unique identifier, or all products)
 * and formats them according to Torob's requirements with proper sorting and pagination.
 */
class GetProductsInTorobFormat
{
    /** @var int Number of items per page for pagination */
    private const PAGINATION_SIZE = 100;

    /** @var string Base URL prefix for product pages */
    private const URL_PREFIX = 'https://khodrox.com/product/';

    /**
     * Main handler method to retrieve products in Torob format.
     *
     * @param array $data Request data containing:
     *                    - 'sort': Sort type (TorobRequestSortType enum value)
     *                    - 'page_urls': Array of product URLs (optional)
     *                    - 'page_uniques': Array of product slugs (optional)
     * 
     * @return LengthAwarePaginator Paginated collection of ProductVariation models
     */
    public function handle(array $data): LengthAwarePaginator
    {
        $requestType = $this->detectRequestType($data);
        $sortType = TorobRequestSortType::from($data['sort']);

        $query = $this->buildQuery($requestType, $data);
        $this->applySorting($query, $sortType);

        return $query->paginate(self::PAGINATION_SIZE);
    }

    /**
     * Detects the type of request based on the presence of specific keys in data array.
     *
     * @param array $data Request data array
     * 
     * @return TorobRequestTypeEnum The detected request type
     */
    private function detectRequestType(array $data): TorobRequestTypeEnum
    {
        return match (true) {
            array_key_exists('page_urls', $data) => TorobRequestTypeEnum::PageUrl,
            array_key_exists('page_uniques', $data) => TorobRequestTypeEnum::PageUnique,
            default => TorobRequestTypeEnum::All,
        };
    }

    /**
     * Builds the appropriate database query based on request type.
     *
     * @param TorobRequestTypeEnum $requestType Type of request to handle
     * @param array $data Request data containing relevant parameters
     * 
     * @return Builder Eloquent query builder instance
     */
    private function buildQuery(TorobRequestTypeEnum $requestType, array $data): Builder
    {
        return match ($requestType) {
            TorobRequestTypeEnum::PageUrl => $this->buildPageUrlQuery($data['page_urls']),
            TorobRequestTypeEnum::PageUnique => $this->buildPageUniqueQuery($data['page_uniques']),
            TorobRequestTypeEnum::All => $this->buildAllProductsQuery(),
        };
    }

    /**
     * Builds query for products filtered by page URLs.
     *
     * @param array $pageUrls Array of complete product page URLs
     * 
     * @return Builder Query builder for products matching the extracted slugs
     */
    private function buildPageUrlQuery(array $pageUrls): Builder
    {
        $slugs = $this->extractSlugsFromUrls($pageUrls);
        return $this->buildQueryWithSlugs($slugs);
    }

    /**
     * Builds query for products filtered by unique page identifiers (slugs).
     *
     * @param array $pageUniques Array of product slugs
     * 
     * @return Builder Query builder for products matching the slugs
     */
    private function buildPageUniqueQuery(array $pageUniques): Builder
    {
        return $this->buildQueryWithSlugs($pageUniques);
    }

    /**
     * Builds query for all available products with pricing information.
     *
     * @return Builder Query builder for all products with prices
     */
    private function buildAllProductsQuery(): Builder
    {
        return ProductVariant::with('product')
            ->whereNotNull('price');
    }

    /**
     * Builds a query for ProductVariation models filtered by product slugs.
     * 
     * Only returns variants that have pricing information and eager loads
     * the related product model.
     *
     * @param array $slugs Array of product slugs to filter by
     * 
     * @return Builder Configured query builder instance
     */
    private function buildQueryWithSlugs(array $slugs): Builder
    {
        return ProductVariant::whereHas('product', fn($query) => $query->whereIn('slug', $slugs))
            ->whereNotNull('price')
            ->with('product');
    }

    /**
     * Extracts product slugs from full product URLs.
     * 
     * Removes the base URL prefix and any trailing slashes to get clean slugs.
     *
     * @param array $urls Array of complete product URLs
     * 
     * @return array Array of extracted product slugs
     */
    private function extractSlugsFromUrls(array $urls): array
    {
        return array_map(
            fn($url) => trim(str_replace(self::URL_PREFIX, '', $url), '/'),
            $urls
        );
    }

    /**
     * Applies sorting to the query based on the specified sort type.
     * 
     * Supports sorting by date added (created_at) or last updated (updated_at),
     * both in descending order.
     *
     * @param Builder $query The query builder to apply sorting to
     * @param TorobRequestSortType $sortType The type of sorting to apply
     * 
     * @return void
     */
    private function applySorting(Builder $query, TorobRequestSortType $sortType): void
    {
        $sortColumn = $sortType != TorobRequestSortType::dateAddedDesc
            ? 'created_at'
            : 'updated_at';

        $query->orderBy($sortColumn, 'desc');
    }
}