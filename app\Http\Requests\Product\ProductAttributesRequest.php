<?php

namespace App\Http\Requests\Product;

use App\Models\User\Shop;
use App\Rules\ValidAttributes;
use Illuminate\Foundation\Http\FormRequest;

class ProductAttributesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "product_slug" => "required|string|exists:products,slug",
            'attributes' => [
                'required',
                'array',
                new ValidAttributes($this->input('product_slug')),
            ],

        ];
    }
}
