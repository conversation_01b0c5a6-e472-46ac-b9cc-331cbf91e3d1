<?php

namespace App\Http\Controllers\Api\V1\Public\Notification;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Notification\SendPushNotification;
use App\Http\Requests\Notification\SendPushNotificationRequest;

/**
 * Controller for managing push notifications
 *
 * @group Push Notification Management
 */
class PushNotificationController extends BaseController
{
    /**
     * Create a new push notification to be sent to a token or topic
     *
     * @param SendPushNotificationRequest $request The validated request
     * @param SendPushNotification $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function store(SendPushNotificationRequest $request, SendPushNotification $action)
    {
        $action->handle($request->validated());

        return $this->sendResponse(
            ['success' => true],
            __('messages.notification.created')
        );
    }
}
