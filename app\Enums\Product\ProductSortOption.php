<?php

namespace App\Enums\Product;

use App\Traits\EnumHelpers;

enum ProductSortOption: string
{
    use EnumHelpers;
    case NEWEST = 'newest';
    case CHEAPEST = 'cheapest';
    case MOST_EXPENSIVE = 'most_expensive';
    case MOST_SALES = 'most_sales';
    case MOST_POPULAR = 'most_popular';




    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return self::tryFrom($value) ?? self::NEWEST;
    }

    public static function labels(): array
    {
        return [
            "newest",
            "cheapest",
            "most_expensive",
            "most_sales",
            "most_popular",
        ];
    }
}