<?php

namespace App\Http\Resources\Discount;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DiscountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->uuid,
            'code' => $this->code,
            'type' => $this->type->label(),
            'amount' => $this->amount,
            'unit' => 'تومان',
            'user' => $this->whenLoaded('user', function () {
                return [
                    'full_name' => $this->user->full_name,
                    'phone' => $this->user->phone,
                ];
            }),
            'is_active' => $this->is_active,
            'expires_at' => $this->shamsi_expires_at,
            'created_at' => $this->shamsi_created_at,
        ];
    }
}
