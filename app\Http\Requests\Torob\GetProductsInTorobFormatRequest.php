<?php

namespace App\Http\Requests\Torob;

use App\Enums\TorobRequestSortType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class GetProductsInTorobFormatRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }


    protected function failedValidation(Validator $validator)
    {
        $exception = (new ValidationException($validator))
            ->status(400);
        throw $exception;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page_urls' => ['array', 'nullable'],
            'page_urls.*' => ['url'],

            'page_uniques' => ['array', 'nullable'],
            'page_uniques.*' => ['string'],

            "page" => 'required|int',
            "sort" => ["required", new Enum(TorobRequestSortType::class)]
        ];
    }


}
