<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class UserBelongsToShop implements ValidationRule
{
    /**
     * Validate that the authenticated user belongs to the given shop.
     *
     * @param  string  $attribute
     * @param  mixed   $value
     * @param  \Closure(string): void  $fail
     * @return void
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Auth::user();

        if (!$user || !$user->shops()->where('id', $value)->exists()) {
            $fail(__('messages.product.user_not_belong_to_shop'));
        }
    }
}
