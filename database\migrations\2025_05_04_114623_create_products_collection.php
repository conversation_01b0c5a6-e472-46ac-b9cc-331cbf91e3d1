<?php

use App\Enums\Product\StatusEnum;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->float('rate')->default(5);

            $table->foreignId('category_id');
            $table->text('description')->nullable();
            $table->string('slug')->unique();
            $table->string('meta_title')->nullable();
            $table->string('meta_description')->nullable();
            $table->foreignId('shop_id');
            $table->enum("seller_status", StatusEnum::values())->default(StatusEnum::PENDING);
            $table->enum("admin_status", StatusEnum::values())->default(StatusEnum::PENDING);
            $table->timestamps();

        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
