<?php

namespace App\Services\Actions\SellerAccounts;

/**
 * Delete Seller Account Action
 *
 * Handles the deletion of a seller account.
 */
class DeleteSellerAccount
{
    /**
     * Handle deleting a seller account
     *
     * @param array $data [
     *   'account' => SellerAccount - The account model instance from route binding
     * ]
     * @return bool
     */
    public function handle(array $data): bool
    {
        $account = $data['account'];
        return $account->delete();
    }
}
