<?php

namespace App\Http\Controllers\Api\V1\Private\Comment;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\UpdateCommentStatusRequest;
use App\Http\Resources\UserInteraction\CommentResource;
use App\Services\Actions\Product\UpdateCommentStatus;
use Symfony\Component\HttpFoundation\JsonResponse;

class CommentStatusController extends BaseController
{
    /**
     * Update the status of a product comment.
     *
     * This method processes the validated request data to update the status
     * of a product comment using the provided action class.
     *
     * @param UpdateCommentStatusRequest $request The incoming request with validated data for updating the comment status.
     * @param UpdateCommentStatus $action The action class responsible for handling the comment status update.
     * @return \Illuminate\Http\JsonResponse A JSON response containing the updated comment resource and a success message.
     */
    public function store(UpdateCommentStatusRequest $request, UpdateCommentStatus $action): JsonResponse
    {
        $comment = $action->handle($request->validated());

        return $this->sendResponse(
            new CommentResource($comment),
            __('messages.comments.updated')
        );
    }
}

