<?php

namespace App\Models\Content;

use App\Traits\Keyword\KeywordRelationsTrait;
use Illuminate\Database\Eloquent\Model;

class Keyword extends Model
{
    use KeywordRelationsTrait;
    
    

    protected $casts = [
        'id' => 'string',
        'keyword_id' => 'string',
        'keywordable_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $fillable = ['title'];

}
