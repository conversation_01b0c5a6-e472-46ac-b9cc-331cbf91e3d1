<?php

namespace App\Enums\Ticket;

use App\Traits\EnumHelpers;
use App\Traits\EnumLabels;

enum TicketTypeEnum: int
{
    use EnumLabels;
    use EnumHelpers;

    case SELLER = 0;
    case SUPPORT = 1;

    public function label(): string
    {
        return match ($this) {
            self::SUPPORT => 'support',
            self::SELLER => 'seller',
        };
    }

    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'support' => self::SUPPORT,
            'seller' => self::SELLER,
            default => self::SUPPORT,
        };
    }
}