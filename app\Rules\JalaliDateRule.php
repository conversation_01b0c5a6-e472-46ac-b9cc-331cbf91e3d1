<?php

namespace App\Rules;

use Closure;
use <PERSON>k<PERSON><PERSON>ser\Verta\Facades\Verta;
use Illuminate\Contracts\Validation\ValidationRule;

class JalaliDateRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            $fail('The :attribute is not a valid Jalali date.');
            return;
        }

        try {
            $jalali = Verta::parse($value);

            // if ($jalali->format($this->format) !== $value) {
            //     $fail('The :attribute is not a valid Jalali date.');
            // }
        } catch (\Throwable $e) {
            $fail('The :attribute is not a valid Jalali date.');
        }

    }
}
