<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\UserInteraction\Question;
use App\Models\UserInteraction\Answer;
use App\Models\User\User;
use Str;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;


/**
 * Seeder for creating questions and answers for products.
 */
class ProductQuestionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates 1-5 random questions for each product with:
     * - Random question body
     * - Random user assignment
     * - One answer per question with random user assignment
     */
    public function run(): void
    {
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('No users found — skipping seeder.');
            return;
        }

        $products = Product::all();

        foreach ($products as $product) {
            $questionCount = rand(1, 5);

            for ($i = 0; $i < $questionCount; $i++) {
                $questionUser = $users->random();
                $answerUser = $users->random();

                $question = $product->questions()->create([
                    'body' => Faker::sentence(),
                    'user_id' => $questionUser->id, // or $questionUser->id
                ]);

                $body = substr(Faker::sentence(), 0, 255, );
                $question->answers()->create([
                    'body' => $body,
                    'user_id' => $answerUser->id,
                ]);
            }
        }
    }
}
