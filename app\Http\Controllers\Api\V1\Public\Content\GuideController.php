<?php

namespace App\Http\Controllers\Api\V1\Public\Content;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Guide\GetProductGuides;
use App\Http\Resources\Content\GuideResource;
use App\Http\Requests\Guide\GetProductGuidesRequest;

/**
 * Controller for retrieving product guides
 *
 * @group Guide Management
 */
class GuideController extends BaseController
{
    /**
     * Get guides related to a specific product
     *
     * @param GetProductGuidesRequest $request The validated request
     * @param string $slug The product slug
     * @param GetProductGuides $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetProductGuidesRequest $request, string $slug, GetProductGuides $action)
    {
        $guides = $action->handle($request->validated());

        return $this->sendResponse(
            GuideResource::collection($guides),
            __('messages.guide.found')
        );
    }
}
