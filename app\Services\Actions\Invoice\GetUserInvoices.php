<?php

namespace App\Services\Actions\Invoice;

use App\Enums\InvoiceDeliveryStatusEnum;
use App\Models\Shopping\Invoice;
use App\Traits\Pagination;

/**
 * Action class for retrieving invoices for a user.
 */
class GetUserInvoices
{
    use Pagination;
    /**
     * Retrieve all invoices for a user with delivery status counts.
     *
     * @param array $data Request data including optional delivery_status filter
     * @return array Array containing paginated invoices and delivery status counts
     */
    public function handle(array $data): array
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();
        $deliveryStatus = isset($data['delivery_status']) ? InvoiceDeliveryStatusEnum::fromString($data['delivery_status']) : null;

        // Get delivery status counts for all user invoices
        $statusCounts = Invoice::where('user_id', $userId)
            ->selectRaw('delivery_status, COUNT(*) as count')
            ->groupBy('delivery_status')
            ->pluck('count', 'delivery_status')
            ->toArray();

        // Format status counts with proper labels
        $deliveryStatusCounts = [
            'in_progress' => $statusCounts[InvoiceDeliveryStatusEnum::IN_PROGRESS->value] ?? 0,
            'sent' => $statusCounts[InvoiceDeliveryStatusEnum::SENT->value] ?? 0,
            'done' => $statusCounts[InvoiceDeliveryStatusEnum::DONE->value] ?? 0,
            'canceled' => $statusCounts[InvoiceDeliveryStatusEnum::CANCELED->value] ?? 0,
        ];

        // Retrieve filtered invoices for the user with their products and transactions
        $builder = Invoice::where('user_id', $userId)
            ->with([
                'products.details',
                'products.productVariant.product.gallery',
                'products.productVariant.attributes',
                'transactions'
            ])
            ->when($deliveryStatus, function ($query) use ($deliveryStatus) {
                return $query->where('delivery_status', $deliveryStatus);
            })
            ->latest();

        $paginatedInvoices = $this->applyPagination($builder, $data);

        return [
            'invoices' => $paginatedInvoices,
            'delivery_status_counts' => $deliveryStatusCounts
        ];
    }
}
