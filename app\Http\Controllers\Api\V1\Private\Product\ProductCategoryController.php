<?php

namespace App\Http\Controllers\Api\V1\Private\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Resources\Product\CategoryAttributeResource;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Services\Actions\Product\AttachCategoryToProduct;
use App\Services\Actions\Product\DetachCategoryToProduct;

class ProductCategoryController extends BaseController
{
    public function store(Product $product, Category $category, AttachCategoryToProduct $action)
    {

        $data = $action->handle(["product" => $product, "category" => $category]);
        return $this->sendResponse(
            CategoryAttributeResource::collection($data),
            __('messages.product.succesful_category_attach')
        );
    }
    public function destroy(Product $product, Category $category, DetachCategoryToProduct $action)
    {
        $data = $action->handle(["product" => $product, "category" => $category]);
        return $this->sendResponse(
            null,
            __('messages.product.succesful_category_detach')
        );
    }
}
