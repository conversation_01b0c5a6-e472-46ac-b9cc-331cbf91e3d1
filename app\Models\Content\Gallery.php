<?php

namespace App\Models\Content;

use App\Traits\Gallery\GalleryRelationsTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Gallery extends Model
{
    use GalleryRelationsTrait;



    protected $fillable = [
        'image_path',
        'caption',
    ];

    protected $casts = [
        'imageable_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    public function getUrl($disk)
    {
        $url = Storage::disk($disk)->url($this->image_path);
        return $url;
    }


}
