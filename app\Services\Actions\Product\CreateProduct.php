<?php

namespace App\Services\Actions\Product;

use App\Enums\Product\StatusEnum;
use App\Models\Product\Category;
use App\Models\Product\Product;

class CreateProduct
{
    /**
     * Handle the creation of a new product.
     *
     * @param array $data Validated product data.
     * @return Product The newly created product instance.
     */
    public function handle(array $data): Product
    {
        // Set default admin status
        $data['admin_status'] = StatusEnum::PENDING;

        // Find category and set category_id
        $category = Category::where('slug', $data['category_slug'])->first();
        $data['category_id'] = $category->id;

        // Generate unique slug
        $data['slug'] = generateRandomSlug();

        // Set seller status (use provided value or default to PENDING)
        $data['seller_status'] = isset($data['seller_status'])
            ? StatusEnum::fromString($data['seller_status'])
            : StatusEnum::PENDING;

        return Product::create($data);
    }
}