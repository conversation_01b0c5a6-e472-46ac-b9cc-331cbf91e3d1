<?php

namespace App\Models\Ticket;

use App\Enums\Ticket\TicketStatusEnum;
use App\Enums\Ticket\TicketTypeEnum;
use App\Models\Content\Gallery;
use App\Models\Shopping\Invoice;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Ticket extends Model
{
    use HasFactory;


    protected $fillable = [
        'title',
        'ticket_status',
        'type',
        'department_id',
        'invoice_id',
        'user_id',
        'assigned_to_id',
    ];


    protected $casts = [
        'ticket_status' => TicketStatusEnum::class,
        'type' => TicketTypeEnum::class,
    ];


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_id');
    }
    public function messages(): HasMany
    {
        return $this->hasMany(TicketMessage::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }



    public function getshamsiCreatedAtAttribute()
    {
        return shamsiDate($this->created_at);
    }


}