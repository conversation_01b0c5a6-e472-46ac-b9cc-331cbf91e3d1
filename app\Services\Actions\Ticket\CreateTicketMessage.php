<?php

namespace App\Services\Actions\Ticket;

use App\Exceptions\BusinessException;
use App\Models\Ticket\Ticket;
use App\Models\Ticket\TicketMessage;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\Response;

class CreateTicketMessage
{
    /**
     * gets a ticket messages
     * @param array $data
     * @return TicketMessage
     */
    public function handle(array $data): TicketMessage
    {

        $userId = auth()->id();
        $ticket = Ticket::find($data['ticket_id']);
        if ($ticket->user_id != $userId) {
            throw new BusinessException(__('messages.ticket.unauthorized'), [], Response::HTTP_UNAUTHORIZED);
        }

        $message = $ticket
            ->messages()
            ->create([
                'message' => $data['message'],
                'user_id' => $userId
            ]);

        if (isset($data['image'])) {
            $image = $data['image'];

            $date = now();
            $path = "{$date->year}/{$date->month}/{$date->day}/{$ticket->id}";
            $path = $image->store($path, 'ticket');

            $message->gallery()->create([
                "image_path" => $path,
            ]);

        }
        return $message;
    }
}
