<?php

namespace App\Traits\ProductDetail;

use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * ProductDetail Relations Trait
 *
 * This trait contains all relationship methods for the ProductDetail model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ProductDetail
 */
trait ProductDetailRelationsTrait
{
    /**
     * Get the product that this detail belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
