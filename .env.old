APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:d2Ic+Ka66gzEuS3+tv4mgEN4RM0m8e2ugS50DgySAho=
APP_DEBUG=true
APP_URL=http://localhost


APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mongodb
MONGODB_URI="mongodb://root:<EMAIL>:32346/my-app?authSource=admin&replicaSet=rs0&directConnection=true"
MONGODB_DATABASE="test-db"

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Firebase Configuration
NOTIFICATION_DRIVER=firebase
FIREBASE_PROJECT_ID=vuefire-5baec
FIREBASE_CLIENT_EMAIL=<EMAIL>
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
NODE_PUSH_URL=http://localhost:3000/
PAYMENT_DRIVER=zarinpal
ZARINPAL_MERCHANT_ID=3c070e80-4eee-4b8b-874f-4eff79cce92e
ZARINPAL_CALLBACK=http://localhost:3000/payment/result
ZARINPAL_GATEWAY_URL=https://sandbox.zarinpal.com/pg/v4/payment/request.json
ZARINPAL_VERIFY_URL=https://sandbox.zarinpal.com/pg/v4/payment/verify.json
