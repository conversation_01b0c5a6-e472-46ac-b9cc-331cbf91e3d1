<?php

namespace App\Services\Actions\Discount;

use App\Models\Discount\DiscountCode;

class UpdateDiscountStatus
{
    /**
     *updates discount code status
     * @param array $data
     * @return DiscountCode|null
     */
    public function handle(array $data): DiscountCode
    {
        $discountCode = DiscountCode::withUser()->where('uuid', $data['discount_code_id'])->first();
        $discountCode->is_active = $data['is_active'];
        $discountCode->save();

        return $discountCode;
    }
}
