<?php

namespace App\Traits\Category;

use App\Models\Product\Attribute;
use App\Models\Product\Category;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Category Relations Trait
 *
 * This trait contains all relationship methods for the Category model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Category
 */
trait CategoryRelationsTrait
{
    /**
     * Get the parent category of this category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get the child categories of this category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get the products that belong to this category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the attributes that belong to this category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attributes()
    {
        return $this->hasMany(Attribute::class, );
    }

    /**
     * Get only searchable parent attributes for this category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function searchableAttributes()
    {
        return $this->hasMany(Attribute::class, )
            ->where('searchable', true)
            ->whereNull('parent_id');


    }





    /**
     * Get all child attribute values for searchable parent attributes belonging to this category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function searchableValues()
    {
        return $this->hasMany(Attribute::class)
            ->whereNotNull('parent_id')
            ->whereHas('parent', function ($query) {
                $query->where('searchable', true);
            });
    }

    /**
     * Get all searchable attributes for this category and all its descendants recursively.
     * This includes both the parent attributes and their values.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllSearchableAttributesRecursively()
    {
        // Get all category IDs recursively (current + all descendants)
        // $allCategoryIds = $this->getAllDescendantIds();

        // Get all searchable parent attributes for these categories with their values
        return Attribute::where('category_id', $this->id)
            ->where('searchable', true)
            ->whereNull('parent_id') // Only parent attributes (titles)
            ->with('values') // Load attribute values
            ->get();
    }

    /**
     * Get all attributes for this category and all its descendants recursively.
     * This includes both the parent attributes and their values.
     *
     * @return Attribute|null
     */
    public function getAllAttributesRecursively()
    {
        // Get all category IDs recursively (current + all descendants)
        // $allCategoryIds = $this->getAllDescendantIds();

        // Get all searchable parent attributes for these categories with their values
        return Attribute::where('category_id', $this->id)
            ->with('values') // Load attribute values
            ->first();
    }

    /**
     * Get all descendant category IDs including this category.
     *
     * @return array
     */
    public function getAllDescendantIds(): array
    {
        // Fetch all categories only once
        $allCategories = Category::all(['id', 'parent_id']);

        $descendantIds = [];

        $traverse = function ($parentId) use (&$traverse, $allCategories, &$descendantIds) {
            foreach ($allCategories as $category) {
                if ((string) $category->parent_id == (string) $parentId) {
                    $descendantIds[] = $category->id;
                    $traverse($category->id); // Recurse
                }
            }
        };

        $descendantIds[] = $this->id; // Include self
        $traverse($this->id);

        return $descendantIds;
    }
}
