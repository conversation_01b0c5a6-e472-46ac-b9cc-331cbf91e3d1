<?php

namespace App\Services\Actions\Categories;

use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Action class to retrieve all Categories
 */
class GetAllCategories
{
    /**
     * Retrieve all Categories
     *
     * @return Collection Collection of searchable attributes
     */
    public function handle(array $data): Collection
    {
        $query = Category::whereNull('parent_id');

        if (isset($data['parent'])) {
            $slug = $data["parent"];
            $query = Category::whereHas('parent', function ($query) use ($slug) {
                $query->where('slug', $slug);
            });
        }

        if (isset($data['search'])) {
            $query->whereLike('title', $data['search'] . "%");
        }
        return $query->with('children')->get();
    }

}
