<?php

namespace App\Services\Actions\Cart;

/**
 * Remove an item from the user's shopping cart.
 */
class RemoveCartItem
{
    /**
     * Handle removal of a cart item based on variant.
     * If the item quantity is 1, it will be removed completely.
     * If the quantity is greater than 1, it will be decreased by 1.
     *
     * @param array $data [
     *   'variant_id' => string
     * ]
     * @return bool
     */
    public function handle(array $data): bool
    {
        $variantId = $data['variant_id'];
        $item = auth()->user()->cart->items()
            ->where('product_variant_id', $variantId)
            ->first();
        // Get the cart item model from the Form Request

        // If quantity is 1, remove the item completely
        if ($item->quantity <= 1) {
            $item->delete();
        } else {
            // Otherwise, decrease the quantity by 1
            $item->quantity -= 1;
            $item->save();
        }

        return true;
    }
}
