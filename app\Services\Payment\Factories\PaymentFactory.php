<?php

namespace App\Services\Payment\Factories;

use App\Services\Payment\Contracts\PaymentContract;
use App\Services\Payment\Drivers\ZarinPalDriver;

/**
 * Payment Factory
 *
 * Factory class for creating payment gateway driver instances.
 */
class PaymentFactory
{
    /**
     * Create a payment gateway driver instance
     *
     * @param string|null $driver The payment driver name (defaults to config value)
     * @return PaymentContract The payment gateway driver instance
     * @throws \Exception If the specified driver is not supported
     */
    public static function create(?string $driver = null): PaymentContract
    {
        if ($driver == null) {
            $driver = config('payment.driver');
        }

        return match ($driver) {
            'zarinpal' => new ZarinPalDriver(),
            default => throw new \Exception("Unsupported payment driver [$driver]")
        };
    }
}
