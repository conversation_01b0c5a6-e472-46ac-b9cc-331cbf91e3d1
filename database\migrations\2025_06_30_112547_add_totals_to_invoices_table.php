<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTotalsToInvoicesTable extends Migration
{
    public function up()
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->unsignedBigInteger('subtotal')->default(0)->after('user_id');
            $table->unsignedBigInteger('discount_amount')->default(0)->after('subtotal');
            $table->unsignedBigInteger('total')->default(0)->after('discount_amount');
        });
    }

    public function down()
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn(['subtotal', 'discount_amount', 'total']);
        });
    }
}