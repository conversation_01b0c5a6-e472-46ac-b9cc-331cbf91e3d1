<?php

namespace App\Models\User;

use App\Traits\User\UserRelationsTrait;
use App\Traits\User\UserAttributesTrait;
use Illuminate\Notifications\Notifiable;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Model;

class User extends Model implements AuthenticatableContract
{
    use Notifiable, Authenticatable, UserRelationsTrait, UserAttributesTrait;

    protected $fillable = [
        'full_name',
        'email',
        'phone',
        'password',
        'national_code',
        'api_key',
        'shamsi_birth_date'
    ];

    protected $hidden = [
        'password',
        'api_key',
    ];

    protected $cast = [
        'birth_date' => 'datetime',
    ];

}
