<?php

namespace App\Http\Requests\Product\Seller;


use App\Enums\Product\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string'],
            'slug' => ['sometimes', 'string', 'max:255'],
            'meta_title' => ['sometimes', 'string', 'max:255'],
            'meta_description' => ['sometimes', 'string', 'max:500'],
            'meta_keywords' => ['sometimes', 'string', 'regex:/^([\pL\pN-]+)(,\s*[\pL\pN-]+)*$/u'],
            'details' => ['nullable', 'array', 'max:10'],
            'details.*.key' => ['required_with:details', 'string', 'max:25'],
            'details.*.value' => ['required_with:details', 'string', 'max:25'],
            'seller_status' => ['sometimes', 'string', StatusEnum::rule()],
            'product_unit_id' => ['sometimes', 'int', 'exists:product_units,id'],
        ];
    }


    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'meta_keywords.regex' => __("messages.product.metakey_keywords_wrong_regex"),
        ];
    }

}
