<?php

namespace App\Http\Controllers\Api\V1\Private\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\CreateProductRequest;
use App\Http\Requests\Product\Seller\GetSellerProductRequest;
use App\Http\Requests\Product\Seller\UpdateProductRequest;
use App\Http\Resources\Product\ProductResource;
use App\Http\Resources\Product\SellerProductsListResource;
use App\Models\Product\Product;
use App\Services\Actions\Product\CreateProduct;
use App\Services\Actions\Product\Seller\GetSellerProducts;
use App\Services\Actions\Product\Seller\UpdateProduct;
use Symfony\Component\HttpFoundation\Response;

class ProductController extends BaseController
{

    /**
     * Store a newly created product in storage.
     *
     * @param \App\Http\Requests\Product\CreateProductRequest $request
     * @param \App\Services\Actions\Product\CreateProduct $action
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(CreateProductRequest $request, CreateProduct $action)
    {

        $product = $action->handle($request->validated());

        return $this->sendResponse(
            new ProductResource($product),
            __("messages.product.created"),
            Response::HTTP_CREATED
        );
    }

    public function update(Product $product, UpdateProductRequest $request, UpdateProduct $action)
    {
        $product = $action->handle([
            "product" => $product,
            ...$request->validated()
        ]);

        return $this->sendResponse(
            new ProductResource($product),
            __("messages.product.updated"),
            Response::HTTP_CREATED
        );
    }

    public function index(GetSellerProductRequest $request,GetSellerProducts $action)
    {
        $products = $action->handle($request->validated());

        return $this->sendResponse(
            new SellerProductsListResource($products),
            __("messages.product.created"),
            Response::HTTP_CREATED
        );
    }
}
