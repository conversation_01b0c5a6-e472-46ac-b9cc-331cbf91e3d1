<?php

namespace App\Http\Controllers\Api\V1\Public\Categories;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Categories\GetAllCategoriesRequest;
use App\Http\Requests\Catergories\GetCategoryChildrenRequest;
use App\Http\Resources\Product\CategoryResource;
use App\Services\Actions\Categories\GetAllCategories;
use App\Services\Actions\Categories\GetCategoryChildren;

/**
 * Controller for retrieving category information and related data
 *
 * @group Category Management
 */
class CategoryController extends BaseController
{
    /**
     * Display all categories
     *
     * Returns a list of all available categories.
     * This endpoint retrieves all categories from the system.
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetAllCategoriesRequest $request, GetAllCategories $action)
    {
        $attributes = $action->handle($request->validated());

        return $this->sendResponse(
            CategoryResource::collection($attributes),
            __('messages.category.found')
        );
    }


    /**
     * Display children  of a category
     *
     * Returns a list of all available child categories of a parent category.
     * This endpoint retrieves children  of a category
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function show(GetCategoryChildrenRequest $request, GetCategoryChildren $action)
    {
        $categories = $action->handle($request->validated());
        return $this->sendResponse(
            CategoryResource::collection($categories),
            __('messages.category.found')
        );
    }
}
