<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\Category;

class ProductCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Assigns random categories to products that don't have any categories yet.
     * Only assigns leaf categories (categories with no children).
     */
    public function run(): void
    {
        // Get all products
        $products = Product::all();

        // Get only leaf categories (categories that don't have children)
        $leafCategories = Category::whereDoesntHave('children')->get();

        if ($leafCategories->isEmpty()) {
            $this->command->warn('No leaf categories found. Make sure to run CategorySeeder first and ensure there are categories without children.');
            return;
        }

        $this->command->info("Found {$leafCategories->count()} leaf categories for product assignment.");
        $this->command->info('Assigning categories to products...');

        foreach ($products as $product) {

            // Check if the product already has categories
            $existingCategories = $product->category()->count();
            if ($existingCategories === 0) {
                // Assign 1-3 random leaf categories to the product
                $numberOfCategories = rand(1, min(3, $leafCategories->count()));
                $randomCategories = $leafCategories->random($numberOfCategories);

                foreach ($randomCategories as $category) {
                    // Attach the category to the product
                    $product->category()->attach($category->id);
                }

                $categoryNames = $randomCategories->pluck('title')->implode(', ');
                $this->command->info("Assigned categories to product '{$product->title}': {$categoryNames}");
            } else {
                $this->command->info("Product already has categories: {$product->title}");
            }
        }

        $this->command->info('Finished assigning categories to products.');
    }
}
