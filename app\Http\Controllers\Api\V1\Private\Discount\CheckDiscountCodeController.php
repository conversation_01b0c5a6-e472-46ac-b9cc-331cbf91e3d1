<?php
namespace App\Http\Controllers\Api\V1\Private\Discount;


use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Discount\CheckDiscountCodeRequest;
use App\Http\Resources\Discount\DiscountResource;
use App\Http\Resources\Discount\TotalAmountResource;
use App\Services\Actions\Discount\CheckDiscountCode;
use Symfony\Component\HttpFoundation\JsonResponse;

class CheckDiscountCodeController extends BaseController
{
    /**
     * updates discount code status
     * @param \App\Http\Requests\Discount\CheckDiscountCodeRequest $request
     * @param \App\Services\Actions\Discount\CheckDiscountCode $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CheckDiscountCodeRequest $request, CheckDiscountCode $action): JsonResponse
    {
        $discountAmount = $action->handle($request->validated());
        return $this->sendResponse(
            new TotalAmountResource(
                $discountAmount
            ),
        );
    }
}
