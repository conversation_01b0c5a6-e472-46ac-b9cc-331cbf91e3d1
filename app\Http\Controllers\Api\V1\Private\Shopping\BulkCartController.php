<?php

namespace App\Http\Controllers\Api\V1\Private\Shopping;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Cart\BulkAddToCart;
use App\Http\Requests\Cart\BulkAddToCartRequest;
use App\Http\Resources\Shopping\CartResource;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing bulk shopping cart operations
 *
 * @group Shopping Cart
 * @authenticated
 */
class BulkCartController extends BaseController
{
    /**
     * Add or update multiple product variants in the shopping cart at once
     * If a product already exists in the cart, its quantity will be set to the given value (not added)
     * If a product doesn't exist in the cart, it will be added with the given quantity
     *
     * @param BulkAddToCartRequest $request The validated request
     * @param BulkAddToCart $action The action service
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(BulkAddToCartRequest $request, BulkAddToCart $action)
    {
        $cart = $action->handle($request->validated());

        return $this->sendResponse(
            new CartResource($cart),
            __('messages.cart.items_updated'),
            Response::HTTP_CREATED
        );
    }
}
