<?php

namespace App\Http\Requests\Product;

use App\Models\Product\Category;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Form request for validating category products retrieval
 */
class GetCategoryProductsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // This is a public endpoint
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $myCustomRules = $this->getAttributeRules($this->slug);
        $rules = [
            'slug' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $category = Category::where('slug', $value)->first();
                    if (!$category) {
                        $fail(__('messages.category.not_found'));
                    }
                },
            ],
            // Include all the same filtering options as GetAllProductsRequest
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1|max:100',
            'search' => 'string|max:255',
            'min_price' => 'integer|min:0',
            'max_price' => [
                'integer',
                'min:0',
                function ($_, $value, $fail) {
                    if ($this->min_price != null && $value < $this->min_price) {
                        $fail(__('messages.product.max_price_greater_than_min'));
                    }
                }
            ],
            'sort' => 'string|in:newest,cheapest,most_expensive,most_sales,most_popular',
            'in_stock_only' => 'sometimes|in:true,false',
            'has_guarantee_only' => 'sometimes|in:true,false',
            ...$myCustomRules,
        ];
        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'max_price.gte' => __('messages.product.max_price_greater_than_min'),
            'limit.max' => __('messages.product.per_page_limit'),
            'sort.in' => __('messages.product.invalid_sort_option'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        foreach ($this->all() as $key => $value) {
            if (str_starts_with($key, 'attribute_') && is_string($value)) {
                $this->merge([
                    $key => explode(',', $value),
                ]);
            }
        }
        $this->merge([
            'slug' => $this->route('slug'),
        ]);
    }

    private function getAttributeRules(string $slug): array
    {
        $category = Category::where('slug', $slug)->firstOrFail();

        $attributes = $category->getAllSearchableAttributesRecursively();
        $rules = [];

        foreach ($attributes as $attribute) {
            $key = 'attribute_' . $attribute->value;

            $valuesArray = $attribute->values instanceof \Illuminate\Support\Collection
                ? $attribute->values->toArray()
                : (array) $attribute->values;

            $titles = array_map(
                fn($item) => $item['title'] ?? null,
                $valuesArray
            );

            $titles = array_filter($titles, fn($val) => is_string($val) && trim($val) != '');

            if (empty($titles)) {
                continue;
            }

            $rules[$key] = ['nullable', 'array'];
            $rules["$key.*"] = ['string', Rule::in($titles)];
        }

        return $rules;
    }



}
