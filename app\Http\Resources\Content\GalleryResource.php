<?php

namespace App\Http\Resources\Content;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Storage;

class GalleryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $url = '';
        if ($this->image_path) {
            $url = Storage::disk($this->getDisk($this->imageable_type))->url($this->image_path);
        } else if ($this->image_url) {
            $url = $this->image_url;
        }
        return [
            "id" => $this->id,
            'url' => $url,
            'caption' => $this->caption ?? null,
        ];
    }

    /**
     * Determine the storage disk name based on the imageable model.
     */
    private function getDisk(object|string $imageable): string
    {
        // Handle if model instance passed
        if (is_object($imageable)) {
            $className = class_basename($imageable);
        } else {
            // Assume it's a class name string
            $className = class_basename($imageable);
        }

        return strtolower($className);
    }
}
