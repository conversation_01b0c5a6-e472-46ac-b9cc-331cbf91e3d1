<?php

namespace App\Enums\User;

use App\Traits\EnumHelpers;

enum UserNotificationTypeEnum: int
{
    use EnumHelpers;

    case INQUIRIES = 1;
    case INVOICES = 2;
    case TRANSACTIONS = 3;
    case SYSTEM = 4;


    public static function labels(): array
    {
        return [
            "inquiries",
            "invoices",
            "transactions",
            "system",
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::INQUIRIES => 'inquiries',
            self::INVOICES => 'invoices',
            self::TRANSACTIONS => 'transactions',
            self::SYSTEM => 'system',
        };
    }


    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'inquiries' => self::INQUIRIES,
            'invoices' => self::INVOICES,
            'transactions' => self::TRANSACTIONS,
            'system' => self::SYSTEM,
        };
    }
}
