<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
public function up(): void
{
    Schema::create('ticket_messages', function (Blueprint $table) {
        $table->id();

        $table->foreignId('ticket_id')->constrained('tickets')->cascadeOnDelete();
        $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();

        $table->boolean('is_admin')->default(false);
        $table->text('message')->nullable();

        $table->timestamp('read_at')->nullable();
        $table->timestamps();
    });
}

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_messages');
    }
};
