<?php

namespace App\Console\Commands;

use App\Models\User\User;
use App\Models\Shopping\Invoice;
use App\Models\Shopping\Transaction;
use App\Models\Notification\FirebaseToken;
use App\Services\Notifications\UserNotificationService;
use App\Enums\PayTypeEnum;
use Illuminate\Console\Command;

class TestNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:notifications {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the notification system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id') ?? 1;

        // Check if user exists
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        $this->info("Testing notifications for user: {$user->full_name} (ID: {$userId})");

        // Create a test Firebase token for the user
        $token = FirebaseToken::create([
            'user_id' => $userId,
            'token' => 'test_token_' . time(),
            'agent' => 'Test Agent',
            'ip' => '127.0.0.1',
            'path' => '/test'
        ]);

        $this->info("Created test Firebase token: {$token->token}");

        // Create a test invoice
        $invoice = Invoice::create([
            'user_id' => $userId,
            'invoice_number' => 'TEST' . time(),
            'status' => PayTypeEnum::PENDING,
            'address' => 'Test Address',
            'receiver_name' => 'Test Receiver',
            'receiver_phone' => '09123456789',
            'province' => 'Tehran',
            'city' => 'Tehran',
            'zip_code' => '1234567890',
            'latitude' => '35.6892',
            'longitude' => '51.3890',
            'subtotal' => 100000,
            'discount_amount' => 0,
            'total' => 100000,
        ]);

        $this->info("Created test invoice: {$invoice->invoice_number}");

        // Test invoice creation notification
        $notificationService = app(UserNotificationService::class);
        $notificationService->sendInvoiceCreated($invoice);
        $this->info("✓ Sent invoice creation notification");

        // Create a test transaction
        $transaction = Transaction::create([
            'user_id' => $userId,
            'payable_id' => $invoice->id,
            'payable_type' => get_class($invoice),
            'status' => PayTypeEnum::PAID,
            'amount' => 100000,
            'payment_method' => 'online',
            'description' => 'Test payment',
            'paid_at' => now(),
        ]);

        // Test payment success notification
        $notificationService->sendInvoicePaymentSuccess($invoice, $transaction);
        $this->info("✓ Sent payment success notification");

        // Test wallet top-up notification
        $walletTransaction = Transaction::create([
            'user_id' => $userId,
            'status' => PayTypeEnum::PAID,
            'amount' => 50000,
            'payment_method' => 'online',
            'description' => 'Test wallet top-up',
            'paid_at' => now(),
        ]);

        $notificationService->sendWalletTopupSuccess($walletTransaction);
        $this->info("✓ Sent wallet top-up notification");

        $this->info("\nNotification test completed successfully!");
        $this->info("Check the user_notifications table and notifications table for the created records.");

        return 0;
    }
}
