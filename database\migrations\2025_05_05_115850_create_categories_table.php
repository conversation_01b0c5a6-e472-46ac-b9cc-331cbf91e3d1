<?php

use App\Enums\Category\CategoryTypeEnum;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Migrations\Migration;


return new class extends Migration {
    public function up(): void
    {
        Schema::create('categories', function ($table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique(); // unique index
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('cascade');
            $table->enum('type', CategoryTypeEnum::labels());
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
