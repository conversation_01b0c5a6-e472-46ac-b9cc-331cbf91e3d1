<?php
namespace App\Models\Product;

use App\Models\Shopping\Invoice;
use Illuminate\Database\Eloquent\Model as Eloquent;

class PurchaseEntry extends Eloquent
{
    
    

    protected $fillable = [
        'product_variant_id',
        'quantity',
        'price',
        'purchased_at',
        'invoice_id',
    ];

    protected $casts = [
        'purchased_at' => 'datetime',
        'invoice_id' => 'string',
        'variant_id' => 'string',
    ];
    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    /**
     * Get the invoice associated with this entry (for sales only).
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Determine if this entry is a purchase (positive quantity) or a sale (negative quantity).
     *
     * @return bool
     */
    public function isPurchase()
    {
        return $this->quantity > 0;
    }

    /**
     * Determine if this entry is a sale (negative quantity).
     *
     * @return bool
     */
    public function isSale()
    {
        return $this->quantity < 0;
    }
    protected static function booted()
    {
        static::saved(function ($reservation) {
            $reservation->updateVariantStock();
        });
    }
    protected function updateVariantStock()
    {
        if ($this->variant) {
            $this->variant->stock = $this->variant->getCurrentQuantityAttribute();
            $this->variant->save();
        }
    }
}
