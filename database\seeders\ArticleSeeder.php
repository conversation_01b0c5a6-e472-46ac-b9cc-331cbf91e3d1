<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Content\Article;
use App\Models\Product\Product;
use Illuminate\Support\Str;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

class ArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * Creates 5 articles with HTML content similar to CKEditor output.
     * Some articles will be related to products, others will be standalone.
     */
    public function run(): void
    {
        $this->command->info('Creating articles...');

        // Get all products to potentially relate articles to
        $products = Product::all();

        // Create 5 articles
        for ($i = 0; $i < 5; $i++) {
            // Generate a Persian title
            $title = Faker::sentence();
            
            // Generate HTML content
            $content = $this->generateHtmlContent();
            
            // Decide if this article should be related to a product
            $shouldRelateToProduct = (bool) rand(0, 1);
            
            if ($shouldRelateToProduct && $products->isNotEmpty()) {
                // Randomly select a product
                $product = $products->random();
                
                // Create article related to the product
                $product->articles()->create([
                    'title' => $title,
                    'content' => $content,
                ]);
                
                $this->command->info("Created article '{$title}' related to product '{$product->title}'");
            } else {
                // Create standalone article
                Article::create([
                    'title' => $title,
                    'content' => $content,
                    'articleable_type' => null,
                    'articleable_id' => null,
                ]);
                
                $this->command->info("Created standalone article '{$title}'");
            }
        }
        
        $this->command->info('Articles created successfully!');
    }
    
    /**
     * Generate HTML content similar to what would be produced by CKEditor.
     * 
     * @return string
     */
    private function generateHtmlContent(): string
    {
        // Array of possible HTML structures to use
        $htmlTemplates = [
            $this->generateArticleTemplate1(),
            $this->generateArticleTemplate2(),
            $this->generateArticleTemplate3(),
            $this->generateArticleTemplate4(),
            $this->generateArticleTemplate5(),
        ];
        
        // Return a random template
        return $htmlTemplates[array_rand($htmlTemplates)];
    }
    
    /**
     * Generate article template 1 - Basic article with heading, paragraphs and image
     */
    private function generateArticleTemplate1(): string
    {
        $heading = Faker::sentence();
        $paragraph1 = Faker::paragraph();
        $paragraph2 = Faker::paragraph();
        $paragraph3 = Faker::paragraph();
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$paragraph1}</p>
<div class="image-container">
    <img src="https://picsum.photos/800/400" alt="تصویر مقاله" />
    <small>توضیحات تصویر</small>
</div>
<p>{$paragraph2}</p>
<p>{$paragraph3}</p>
HTML;
    }
    
    /**
     * Generate article template 2 - Article with heading, paragraphs, and list
     */
    private function generateArticleTemplate2(): string
    {
        $heading = Faker::sentence();
        $paragraph1 = Faker::paragraph();
        $paragraph2 = Faker::paragraph();
        
        // Generate list items
        $listItems = '';
        for ($i = 0; $i < 4; $i++) {
            $listItems .= '<li>' . Faker::sentence() . '</li>';
        }
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$paragraph1}</p>
<h3>موارد مهم</h3>
<ul>
    {$listItems}
</ul>
<p>{$paragraph2}</p>
HTML;
    }
    
    /**
     * Generate article template 3 - Article with multiple headings and blockquote
     */
    private function generateArticleTemplate3(): string
    {
        $mainHeading = Faker::sentence();
        $subHeading1 = Faker::sentence();
        $subHeading2 = Faker::sentence();
        $paragraph1 = Faker::paragraph();
        $paragraph2 = Faker::paragraph();
        $paragraph3 = Faker::paragraph();
        $quote = Faker::sentence();
        
        return <<<HTML
<h1>{$mainHeading}</h1>
<p>{$paragraph1}</p>
<h3>{$subHeading1}</h3>
<p>{$paragraph2}</p>
<blockquote>
    <p>{$quote}</p>
</blockquote>
<h3>{$subHeading2}</h3>
<p>{$paragraph3}</p>
HTML;
    }
    
    /**
     * Generate article template 4 - Article with table
     */
    private function generateArticleTemplate4(): string
    {
        $heading = Faker::sentence();
        $paragraph1 = Faker::paragraph();
        $paragraph2 = Faker::paragraph();
        
        // Generate table rows
        $tableRows = '';
        for ($i = 0; $i < 3; $i++) {
            $tableRows .= '<tr>
                <td>' . Faker::word() . '</td>
                <td>' . Faker::word() . '</td>
                <td>' . Faker::sentence() . '</td>
            </tr>';
        }
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$paragraph1}</p>
<table class="table">
    <thead>
        <tr>
            <th>عنوان اول</th>
            <th>عنوان دوم</th>
            <th>توضیحات</th>
        </tr>
    </thead>
    <tbody>
        {$tableRows}
    </tbody>
</table>
<p>{$paragraph2}</p>
HTML;
    }
    
    /**
     * Generate article template 5 - Article with code block and numbered list
     */
    private function generateArticleTemplate5(): string
    {
        $heading = Faker::sentence();
        $paragraph1 = Faker::paragraph();
        $paragraph2 = Faker::paragraph();
        
        // Generate list items
        $listItems = '';
        for ($i = 0; $i < 3; $i++) {
            $listItems .= '<li>' . Faker::sentence() . '</li>';
        }
        
        $codeExample = "function example() {\n    console.log('مثال کد');\n    return true;\n}";
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$paragraph1}</p>
<h4>مراحل انجام:</h4>
<ol>
    {$listItems}
</ol>
<p>{$paragraph2}</p>
<pre><code>{$codeExample}</code></pre>
HTML;
    }
}
