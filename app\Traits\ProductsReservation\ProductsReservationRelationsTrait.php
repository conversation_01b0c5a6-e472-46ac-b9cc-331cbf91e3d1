<?php

namespace App\Traits\ProductsReservation;

use App\Models\Product\ProductVariant;
use App\Models\Shopping\Invoice;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * ProductsReservation Relations Trait
 *
 * This trait contains all relationship methods for the ProductsReservation model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ProductsReservation
 */
trait ProductsReservationRelationsTrait
{
    /**
     * Get the user that made this reservation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the invoice associated with this reservation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the product variation that is being reserved.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id', 'id');
    }
}
