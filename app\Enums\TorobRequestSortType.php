<?php

namespace App\Enums;

/**
 * Enum for allowed Torob API request input types
 */
enum TorobRequestSortType: string
{
    case dateAddedDesc = 'date_added_desc';
    case dateUpdatedDesc = 'date_updated_desc';

    /**
     * Check if the given value is a valid TorobRequestType
     */
    public static function isValid(string $value): bool
    {
        return in_array($value, array_column(self::cases(), 'value'));
    }
}