<?php

namespace App\Enums\Discount;

use App\Traits\EnumHelpers;
use App\Traits\EnumLabels;

enum DiscountTypeEnum: int
{
    use EnumLabels;
    use EnumHelpers;
    case FIXED = 0;
    case PERCENT = 1;

    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'fixed' => self::FIXED,
            'percent' => self::PERCENT,
            default => self::PERCENT,
        };
    }
}
