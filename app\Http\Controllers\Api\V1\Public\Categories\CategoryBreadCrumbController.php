<?php

namespace App\Http\Controllers\Api\V1\Public\Categories;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\Categories\CategoryBreadCrumbResource;
use App\Models\Product\Category;
use App\Services\Actions\Categories\GetCategoryBreadCrumb;
use Symfony\Component\HttpFoundation\JsonResponse;

class CategoryBreadCrumbController extends BaseController
{
    public function index(Category $category, GetCategoryBreadCrumb $action): JsonResponse
    {
        $breadCrumb = $action->handle($category);

        return $this->sendResponse(CategoryBreadCrumbResource::collection($breadCrumb), "موفق");
    }
}
