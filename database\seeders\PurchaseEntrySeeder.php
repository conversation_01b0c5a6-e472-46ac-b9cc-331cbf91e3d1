<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\ProductVariant;
use Illuminate\Support\Carbon;

/**
 * Seeder for creating purchase entries for product variants.
 */
class PurchaseEntrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates purchase entries for 5 random product variants:
     * - 3 purchase entries per variation
     * - Random quantities between 5-20
     * - Random prices between $1-$20
     * - Purchase dates within the last 30 days
     * - No invoice ID (these are inventory additions, not sales)
     */
    public function run(): void
    {
        $variants = ProductVariant::all()->shuffle()->take(5);

        // Create purchase entries (positive quantities)
        foreach ($variants as $variant) {
            foreach (range(1, 3) as $_) {
                $variant->purchases()->create([
                    'quantity' => rand(5, 20), // Positive quantity for purchases
                    'price' => rand(100, 2000) / 100,
                    'purchased_at' => Carbon::now()->subDays(rand(0, 30)),
                    'invoice_id' => null, // No invoice for purchases
                ]);
            }
        }


    }
}
