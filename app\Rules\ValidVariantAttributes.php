<?php

namespace App\Rules;

use App\Models\Product\Category;
use App\Models\Product\Product;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidVariantAttributes implements ValidationRule
{

    protected string $productSlug;

    public function __construct(string $productSlug)
    {
        $this->productSlug = $productSlug;
    }
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $product = Product::where("slug", $this->productSlug)->first();

        $category = $product->category;
        $attributes = $category->getAllAttributesRecursively();


        foreach ($value as $attribute) {

            $englishTitle = $attribute['english_title'];
            $actualValue = $attribute['value'];


            $matchedAttribute = collect($attributes)->firstWhere('value', $englishTitle);

            if (!$matchedAttribute) {

                $fail("Attribute '$englishTitle' is not a valid attribute.");
                continue;
            }

            $validOption = collect($matchedAttribute['values'] ?? [])->firstWhere('title', $actualValue);

            if (!$validOption) {
                $fail("Value '$actualValue' is not valid for attribute '$englishTitle'.");
            }

        }
    }
}
