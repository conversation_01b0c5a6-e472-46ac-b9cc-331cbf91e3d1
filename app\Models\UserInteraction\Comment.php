<?php

namespace App\Models\UserInteraction;

use App\Enums\Product\StatusEnum;
use App\Traits\Comment\CommentAttributesTrait;
use App\Traits\Comment\CommentRelationsTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Comment Model
 *
 * Represents a comment that can be associated with various models through a polymorphic relationship.
 * Comments can be nested (parent-child relationship) to support replies.
 *
 * @property string $id MongoDB document ID
 * @property string $body The content of the comment
 * @property string|null $user_id The ID of the user who created the comment (nullable)
 * @property string|null $parent_id The ID of the parent comment if this is a reply (nullable)
 * @property string $commentable_type The class name of the parent model
 * @property string $commentable_id The ID of the parent model
 * @property string|null $ip_address The IP address of the commenter (nullable)
 * @property string|null $user_agent The user agent of the commenter (nullable)
 * @property boolean|null $has_bought Whether the user has bought the product (only for product comments, nullable)
 * @property integer|null $rate Rating on a scale from 1 to 5 (only for product comments, nullable)
 * @property \Carbon\Carbon $created_at When the comment was created
 * @property \Carbon\Carbon $updated_at When the comment was last updated
 */
class Comment extends Model
{
    use CommentRelationsTrait, CommentAttributesTrait;

    /**
     * The attributes that are mass assignable.
     *
     * Note: has_bought is only used when the comment is for a product.
     * It indicates whether the user has purchased the product they're commenting on.
     *
     * Note: rate is only used when the comment is for a product.
     * It represents the user's rating of the product on a scale from 1 to 5.
     */
    protected $fillable = [
        'body',
        'user_id',
        'parent_id',
        'ip_address',
        'user_agent',
        'has_bought',
        'status',
        'rate'
    ];

    protected $casts = [
        'has_bought' => 'boolean',
        'rate' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => StatusEnum::class,
    ];

}
