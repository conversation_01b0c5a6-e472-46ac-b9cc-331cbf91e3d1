<?php

namespace App\Http\Controllers\Api\V1\Private\Shopping;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Transactions\CreateInvoicePayment;
use App\Http\Requests\Transactions\CreateInvoicePaymentRequest;
use App\Http\Resources\Shopping\TransactionResource;

/**
 * Controller for managing invoice payment transactions
 *
 * @group Invoice Payment Management
 * @authenticated
 */
class InvoicePaymentController extends BaseController
{
    /**
     * Create a new payment transaction for an invoice.
     *
     * Initiates the payment process for an invoice and returns the payment gateway URL.
     *
     * @param CreateInvoicePaymentRequest $request The validated request
     * @param CreateInvoicePayment $createInvoicePayment The invoice payment creation action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateInvoicePaymentRequest $request, CreateInvoicePayment $createInvoicePayment)
    {
        $transaction = $createInvoicePayment->handle($request->validated());

        $resource = new TransactionResource(
            $transaction['model'],
            $transaction['gateway_url'] ?? null,

        );

        // Return the response with a single success message
        return $this->sendResponse($resource, __('messages.transaction.invoice_payment_created'));
    }
}
