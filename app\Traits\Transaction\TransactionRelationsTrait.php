<?php

namespace App\Traits\Transaction;

use App\Models\Shopping\Invoice;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Transaction Relations Trait
 *
 * This trait contains all relationship methods for the Transaction model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Transaction
 */
trait TransactionRelationsTrait
{
    /**
     * Get the parent model that this transaction belongs to.
     * This can be any model that the transaction is associated with (Invoice, User, etc.).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user that owns this transaction.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
