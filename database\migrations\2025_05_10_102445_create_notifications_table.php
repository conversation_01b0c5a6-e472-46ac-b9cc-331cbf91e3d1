<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function ($table) {
            $table->id();
            $table->string('title');
            $table->string('description');
            $table->string('image');
            $table->json('payload');
            $table->timestamp('time_to_send');
            $table->timestamp('sent_time');
            $table->string('type');
            $table->string('target');
            $table->string('status');
            $table->string('error')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
