<?php

namespace App\Models\SellerAccounting;

use App\Models\Content\Gallery;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class SupportingBank extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
    ];

    public function logo(): MorphOne
    {
        return $this->morphOne(Gallery::class, 'imageable');
    }
}