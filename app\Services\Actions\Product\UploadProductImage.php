<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;

class UploadProductImage
{
    /**
     * Handles uploading a product image, stores it in a structured path,
     * creates a gallery record, and returns the updated product with its relations.
     *
     * @param array{
     *     product: \App\Models\Product\Product,
     *     image: \Illuminate\Http\UploadedFile,
     *     caption: string
     * } $data An array containing the product instance, image file, and caption.
     *
     * @return \App\Models\Product\Product The product model with loaded relations after image upload.
     */
    public function handle(array $data): Product
    {
        $product = $data['product'];

        /** @var \Illuminate\Http\UploadedFile $file */
        $file = $data['image'];
        $caption = $data['caption'];

        $date = now();
        $slug = $product->slug;
        $path = "{$date->year}/{$date->month}/{$date->day}/{$slug}";

        $path = $file->store($path, 'product');

        $product->gallery()->create([
            "image_path" => $path,
            "caption" => $caption,
        ]);

        return $product;
    }
}
