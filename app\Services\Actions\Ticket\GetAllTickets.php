<?php

namespace App\Services\Actions\Ticket;

use App\Enums\Ticket\TicketStatusEnum;
use App\Enums\Ticket\TicketTypeEnum;
use App\Models\Ticket\Ticket;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class GetAllTickets
{
    /**
     * gets all tickets related to user
     * @param array $data
     * @return LengthAwarePaginator
     */
    public function handle(array $data): LengthAwarePaginator
    {
        $type = isset($data['type']) ? TicketTypeEnum::fromString($data['type']) : null;
        $status = isset($data['status']) ? TicketStatusEnum::fromString($data['status']) : null;

        $limit = $data['limit'] ?? 10;
        $page = $data['page'] ?? null;

        $userId = auth()->id();
        $query = Ticket::where('user_id', $userId);

        if ($type !== null) {
            $query->where('type', $type);
        }

        if ($status !== null) {
            $query->where('ticket_status', $status);
        }
        return $query->latest()
            ->paginate($limit, ['*'], 'page', $page);

    }
}
