<?php

namespace App\Services\Actions\Notification;

use App\Models\Notification\FirebaseToken;

/**
 * Action class for storing Firebase Cloud Messaging tokens.
 *
 * This class handles the storage of FCM tokens from client devices,
 * ensuring that duplicate tokens are not stored.
 */
class StoreClientFirebaseToken
{
    /**
     * Store a Firebase token in the database.
     *
     * If the token already exists, returns the existing record.
     * Otherwise, creates a new record with the provided data.
     *
     * @param array $data Token data including:
     *                    - token: The Firebase token (required)
     *                    - agent: User agent (optional, falls back to request user agent)
     *                    - ip: IP address (optional, falls back to request IP)
     *                    - path: Request path (optional, falls back to current request path)
     * @return FirebaseToken The stored token record
     */
    public function handle(array $data): FirebaseToken
    {

        // Check if token already exists
        $existing = FirebaseToken::where('token', $data['token'])->first();
        if ($existing) {
            return $existing; // skip insert, return existing
        }
        return FirebaseToken::create([
            'agent' => $data['agent'] ?? request()->userAgent(),
            'ip' => $data['ip'] ?? request()->ip(),
            'path' => $data['path'] ?? request()->path(),
            'token' => $data['token'],
        ]);
    }
}
