<?php

namespace App\Console\Commands;

use App\Models\Product\Product;
use Illuminate\Console\Command;

class CalculateProductsRatings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calculate-products-ratings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Product::chunk(100, function ($products) {
            foreach ($products as $product) {

                $averageRate = $product->comments()
                    ->where('has_bought', true)
                    ->avg('rate');
                $rate = 5;
                if ($averageRate && $averageRate > 0) {
                    $rate = (float) number_format($averageRate, 2);
                }
                $product->rate = $rate;
                $product->save();
            }
        });

        $this->info('Product rates updated successfully.');
    }
}
