<?php

namespace App\Http\Controllers\Api\V1\Private\Torob;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Torob\GetProductsInTorobFormatRequest;
use App\Http\Resources\Torob\GetProductsInTorobFormatResource;
use App\Services\Actions\Torob\GetProductsInTorobFormat;

class TorobController extends BaseController
{
    public function store(GetProductsInTorobFormatRequest $request, GetProductsInTorobFormat $action)
    {
        $data = $action->handle($request->validated());
        return $this->sendResponse(
            new GetProductsInTorobFormatResource($data),
            __('messages.product.found'),

        );
    }
}
