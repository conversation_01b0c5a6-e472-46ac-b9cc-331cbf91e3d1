<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Content\Guide;
use App\Models\Product\Product;
use Illuminate\Support\Str;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

class GuideSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * Creates guides for products with HTML content.
     * Each product will have 1-3 guides with different types of content.
     */
    public function run(): void
    {
        $this->command->info('ایجاد راهنماهای محصول...');

        // Get all products
        $products = Product::all();
        
        if ($products->isEmpty()) {
            $this->command->error('هیچ محصولی یافت نشد. ابتدا محصولات را ایجاد کنید.');
            return;
        }
        
        // Clear existing guides
        Guide::truncate();
        
        // Create guides for each product
        foreach ($products as $product) {
            // Randomly decide how many guides to create (1-3)
            $guidesCount = rand(1, 3);
            
            $this->command->info("ایجاد {$guidesCount} راهنما برای محصول '{$product->title}'");
            
            // Create guides with different templates
            for ($i = 0; $i < $guidesCount; $i++) {
                $order = $i + 1; // Order starts from 1
                
                // Choose a template based on the guide number
                switch ($i) {
                    case 0:
                        $title = 'راهنمای استفاده';
                        $content = $this->generateUsageGuide($product->title);
                        break;
                    case 1:
                        $title = 'راهنمای نگهداری';
                        $content = $this->generateMaintenanceGuide($product->title);
                        break;
                    case 2:
                        $title = 'نکات مهم';
                        $content = $this->generateTipsGuide($product->title);
                        break;
                    default:
                        $title = 'راهنمای عمومی';
                        $content = $this->generateUsageGuide($product->title);
                }
                
                // Create the guide
                $product->guides()->create([
                    'title' => $title,
                    'content' => $content,
                    'order' => $order,
                ]);
                
                $this->command->info("راهنمای '{$title}' برای محصول '{$product->title}' ایجاد شد");
            }
        }
        
        $this->command->info('راهنماهای محصول با موفقیت ایجاد شدند!');
    }
    
    /**
     * Generate usage guide content
     * 
     * @param string $productTitle
     * @return string HTML content
     */
    private function generateUsageGuide(string $productTitle): string
    {
        $heading = 'نحوه استفاده از ' . $productTitle;
        $intro = Faker::paragraph();
        $step1 = Faker::sentence();
        $step2 = Faker::sentence();
        $step3 = Faker::sentence();
        $conclusion = Faker::paragraph();
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$intro}</p>
<div class="steps">
    <h3>مراحل استفاده</h3>
    <ol>
        <li>{$step1}</li>
        <li>{$step2}</li>
        <li>{$step3}</li>
    </ol>
</div>
<div class="image-container">
    <img src="https://picsum.photos/800/400" alt="تصویر راهنمای استفاده" />
    <small>تصویر نمونه استفاده از محصول</small>
</div>
<p>{$conclusion}</p>
HTML;
    }
    
    /**
     * Generate maintenance guide content
     * 
     * @param string $productTitle
     * @return string HTML content
     */
    private function generateMaintenanceGuide(string $productTitle): string
    {
        $heading = 'نحوه نگهداری از ' . $productTitle;
        $intro = Faker::paragraph();
        $tip1 = Faker::sentence();
        $tip2 = Faker::sentence();
        $tip3 = Faker::sentence();
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$intro}</p>
<div class="maintenance-tips">
    <h3>نکات نگهداری</h3>
    <ul>
        <li><strong>تمیز کردن:</strong> {$tip1}</li>
        <li><strong>نگهداری:</strong> {$tip2}</li>
        <li><strong>شرایط محیطی:</strong> {$tip3}</li>
    </ul>
</div>
<div class="warning">
    <h4>هشدار</h4>
    <p>از قرار دادن محصول در معرض نور مستقیم خورشید یا رطوبت زیاد خودداری کنید.</p>
</div>
HTML;
    }
    
    /**
     * Generate tips guide content
     * 
     * @param string $productTitle
     * @return string HTML content
     */
    private function generateTipsGuide(string $productTitle): string
    {
        $heading = 'نکات مهم درباره ' . $productTitle;
        $intro = Faker::paragraph();
        $tip1 = Faker::sentence();
        $tip2 = Faker::sentence();
        $tip3 = Faker::sentence();
        
        return <<<HTML
<h2>{$heading}</h2>
<p>{$intro}</p>
<div class="tips">
    <div class="tip">
        <h4>نکته ۱</h4>
        <p>{$tip1}</p>
    </div>
    <div class="tip">
        <h4>نکته ۲</h4>
        <p>{$tip2}</p>
    </div>
    <div class="tip">
        <h4>نکته ۳</h4>
        <p>{$tip3}</p>
    </div>
</div>
<div class="pro-tip">
    <h4>توصیه ویژه</h4>
    <p>برای بهترین نتیجه، از این محصول همراه با سایر محصولات مرتبط استفاده کنید.</p>
</div>
HTML;
    }
}
