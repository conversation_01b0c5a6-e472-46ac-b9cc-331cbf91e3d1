<?php

namespace App\Rules;

use App\Models\Product\Category;
use App\Models\Product\Product;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidAttributes implements ValidationRule
{

    protected string $productSlug;

    public function __construct(string $productSlug)
    {
        $this->productSlug = $productSlug;
    }
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $product = Product::where("slug", $this->productSlug)->first();




        $category = $product->category;
        $attributes = [];

            $attributes = array_merge($attributes, $category->getAllAttributesRecursively()->toArray());


        foreach ($value as $inputKey => $inputValue) {

            $matchedAttribute = collect($attributes)->firstWhere('value', $inputKey);

            if (!$matchedAttribute) {

                $fail("Attribute '$inputKey' is not a valid attribute.");
                continue;
            }

            $validOption = collect($matchedAttribute['values'] ?? [])->firstWhere('title', $inputValue);

            if (!$validOption) {
                $fail("Value '$inputValue' is not valid for attribute '$inputKey'.");
            }
        }

    }
}
