<?php

namespace Database\Seeders\FlowSeeder;

use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\User\Shop;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{

    public function run(): void
    {
        $imageUrls = [
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
        ];
        $pcVariations = [
            "برند سی پی یو" => [
                "english_title" => "CPU_brands",
                "values" => ["اینتل", "ای‌ام‌دی", "آرم"]
            ],
            "تعداد هسته سی پی یو" => [
                "english_title" => "CPU_cores",
                "values" => ["دوال‌کُور", "کواڈکُور", "هکسا‌کُور", "اکتا‌کُور"]
            ],
            "سرعت کلاک سی پی یو" => [
                "english_title" => "CPU_clock_speed",
                "values" => ["3.0 گیگاهرتز", "4.5 گیگاهرتز"]
            ],


            "ظرفیت رم" => [
                "english_title" => "RAM_capacity",
                "values" => ["8 گیگابایت", "16 گیگابایت", "32 گیگابایت", "64 گیگابایت"]
            ],

            "نوع رم" => [
                "english_title" => "RAM_type",
                "values" => ["DDR3", "DDR4", "DDR5"]
            ],


            "ظرفیت SSD" => [
                "english_title" => "Storage_SSD_capacity",
                "values" => ["250 گیگابایت", "500 گیگابایت", "1 ترابایت", "2 ترابایت"]
            ],

            "حافظه گرافیکی" => [
                "english_title" => "GPU_vram",
                "values" => ["2 گیگابایت", "4 گیگابایت", "8 گیگابایت", "16 گیگابایت"]
            ],

        ];

        $products = [
            [
                "title" => "پردازنده Intel Core i9-12900K",
                "description" => "پردازنده Intel Core i9-12900K از نسل دوازدهم اینتل با 16 هسته و 24 رشته است که عملکرد استثنائی در انجام چندین کار همزمان و بازی‌های کامپیوتری ارائه می‌دهد."
            ],
            [
                "title" => "پردازنده AMD Ryzen 9 5900X",
                "description" => "پردازنده AMD Ryzen 9 5900X با 12 هسته برای گیمرها و تولیدکنندگان محتوا طراحی شده و عملکرد بی‌نظیری در پردازش چند رشته‌ای دارد."
            ],
            [
                "title" => "کارت گرافیک NVIDIA GeForce RTX 3080",
                "description" => "کارت گرافیک NVIDIA GeForce RTX 3080 با عملکرد گرافیکی پیشرفته و پشتیبانی از ردیابی پرتو به صورت همزمان، گرافیک‌های مبتنی بر AI و 10 گیگابایت VRAM GDDR6X ارائه می‌دهد."
            ],
            [
                "title" => "رم Corsair Vengeance LPX 16GB DDR4",
                "description" => "رم Corsair Vengeance LPX با ظرفیت 16 گیگابایت از نوع DDR4، انتخابی عالی برای بازی و انجام چندین کار همزمان."
            ],
            [
                "title" => "SSD Kingston A2000 1TB NVMe",
                "description" => "SSD Kingston A2000 با ظرفیت 1 ترابایت و پشتیبانی از تکنولوژی NVMe، سرعت خواندن و نوشتن فوق‌العاده‌ای را برای بوت سریع‌تر سیستم و بارگذاری بازی‌ها ارائه می‌دهد."
            ],
            [
                "title" => "هارد دیسک Western Digital 2TB",
                "description" => "هارد دیسک 2 ترابایتی Western Digital فضای ذخیره‌سازی زیادی برای تمام داده‌ها و رسانه‌های شما فراهم می‌کند و عملکرد و قابلیت اطمینان بالایی دارد."
            ],
            [
                "title" => "مادربورد ASUS ROG Strix Z590-E Gaming",
                "description" => "مادربورد ASUS ROG Strix Z590-E مخصوص بازی است که از پردازنده‌های نسل یازدهم اینتل، حافظه با سرعت بالا و PCIe 4.0 پشتیبانی می‌کند."
            ],
            [
                "title" => "کیس NZXT H510 Mid Tower",
                "description" => "کیس NZXT H510 طراحی مدرن و شیک دارد و برای سیستم‌های گیمینگ و عملکرد بالا گزینه‌ای عالی است."
            ],
            [
                "title" => "منبع تغذیه Corsair RM750x 750W",
                "description" => "منبع تغذیه Corsair RM750x با توان 750 وات و بازدهی 80+ Gold گزینه‌ای مناسب برای سیستم‌های با مصرف برق بالا است."
            ],
            [
                "title" => "پاوربانک Anker PowerCore 20000mAh",
                "description" => "پاوربانک Anker PowerCore با ظرفیت 20000 میلی‌آمپر ساعت، انرژی کافی برای شارژ دستگاه‌های مختلف شما در سفرها فراهم می‌آورد."
            ],
            [
                "title" => "کارت گرافیک AMD Radeon RX 6800 XT",
                "description" => "کارت گرافیک AMD Radeon RX 6800 XT با معماری RDNA2 برای عملکرد گرافیکی بالای بازی‌ها و کارهای حرفه‌ای طراحی شده است."
            ],
            [
                "title" => "رم G.SKILL Ripjaws V 32GB DDR4",
                "description" => "رم G.SKILL Ripjaws V با ظرفیت 32 گیگابایت از نوع DDR4، سرعت بالا و عملکرد عالی برای گیمرها و تولیدکنندگان محتوا."
            ],
            [
                "title" => "مادربورد MSI MPG B550 Gaming Edge WiFi",
                "description" => "مادربورد MSI MPG B550 Gaming Edge WiFi از پردازنده‌های AMD Ryzen پشتیبانی می‌کند و ویژگی‌هایی مانند Wi-Fi و اتصالات سریع دارد."
            ],
            [
                "title" => "هارد دیسک Seagate Barracuda 1TB",
                "description" => "هارد دیسک Seagate Barracuda با ظرفیت 1 ترابایت، گزینه‌ای با قیمت مناسب و عملکرد عالی برای ذخیره‌سازی داده‌ها."
            ],
            [
                "title" => "کارت گرافیک EVGA GeForce RTX 3070",
                "description" => "کارت گرافیک EVGA GeForce RTX 3070 با تکنولوژی ray tracing و عملکرد بسیار خوب برای گیمرها و تولیدکنندگان محتوا طراحی شده است."
            ],
            [
                "title" => "منبع تغذیه EVGA SuperNOVA 650 G5 650W",
                "description" => "منبع تغذیه EVGA SuperNOVA 650 G5 با توان 650 وات و بازدهی 80+ Gold برای سیستم‌های گیمینگ و حرفه‌ای مناسب است."
            ],
            [
                "title" => "کیس Fractal Design Meshify C",
                "description" => "کیس Fractal Design Meshify C با طراحی زیبا و جریان هوای عالی، گزینه‌ای مناسب برای سیستم‌های گیمینگ و حرفه‌ای است."
            ],
            [
                "title" => "کیبورد Logitech G Pro X",
                "description" => "کیبورد Logitech G Pro X یک کیبورد مکانیکی مخصوص بازی است که کلیدهای قابل تعویض و نورپردازی RGB دارد."
            ],
            [
                "title" => "ماوس SteelSeries Rival 600",
                "description" => "ماوس SteelSeries Rival 600 یک ماوس گیمینگ با حسگر دقت بالا و وزن قابل تنظیم برای عملکرد عالی در بازی‌های حرفه‌ای."
            ],
            [
                "title" => "مانیتور ASUS ROG Swift PG259QN",
                "description" => "مانیتور ASUS ROG Swift PG259QN با صفحه نمایش 24.5 اینچی و نرخ تجدید 360Hz، تجربه بی‌نظیری از بازی‌های سریع و روان ارائه می‌دهد."
            ],
            [
                "title" => "هدست Corsair HS70 Wireless",
                "description" => "هدست Corsair HS70 Wireless با صدای فراگیر 7.1 و اتصال بی‌سیم 2.4GHz، تجربه‌ای عالی برای گیمینگ فراهم می‌آورد."
            ],
            [
                "title" => "وبکم Logitech C922 Pro Stream",
                "description" => "وبکم Logitech C922 Pro Stream با کیفیت 1080p و نورپردازی عالی برای استریمینگ و تماس‌های ویدیویی ایده‌آل است."
            ],
            [
                "title" => "پردازنده Intel Core i5-11600K",
                "description" => "پردازنده Intel Core i5-11600K برای گیمینگ و کارهای روزمره عملکرد عالی دارد و از 6 هسته و 12 رشته پشتیبانی می‌کند."
            ],
            [
                "title" => "رم HyperX Fury 16GB DDR4",
                "description" => "رم HyperX Fury با ظرفیت 16 گیگابایت و سرعت 3200 مگاهرتز، عملکرد بالا و طراحی زیبا برای سیستم‌های گیمینگ ارائه می‌دهد."
            ],
            [
                "title" => "کیس Cooler Master MasterBox Q300L",
                "description" => "کیس Cooler Master MasterBox Q300L با طراحی جذاب و سایز کوچک برای سیستم‌های گیمینگ و خانه‌های کوچک مناسب است."
            ]
        ];

        $pcCategory = Category::where('slug', 'کامپیوتر')->first();
        foreach ($pcVariations as $title => $pcVariation) {

            $parentAttribute = $pcCategory->attributes()->create([
                "title" => $title,
                'value' => $pcVariation['english_title'],
                "searchable" => true,
                "parent_id" => null,
            ]);

            foreach ($pcVariation['values'] as $valueVariation) {
                $pcCategory->attributes()->create([
                    "title" => $valueVariation,
                    "searchable" => false,
                    "parent_id" => $parentAttribute->id,
                ]);
            }
        }
        foreach ($products as $product) {
            $shop = Shop::first();
            $createProdcut = Product::updateOrCreate(
                [
                    "slug" => str_replace(' ', '-', $product['title'])
                ],
                [
                    'category_id' => $pcCategory->id,
                    "shop_id" => $shop->id,
                    "title" => $product['title'],
                    "description" => $product['description'],
                ]
            );

            $random_products = [];

            foreach ($pcVariations as $attribute_title => $variation) {
                $random_value = $variation['values'][array_rand($variation['values'])];

                $random_products[] = [
                    "attribute_title" => $attribute_title,
                    "attribute_value" => $random_value,
                    "attribute_type" => $variation['english_title']
                ];
            }
            foreach (collect($imageUrls)->shuffle()->take(3) as $url) {
                $createProdcut->gallery()->create([
                    'image_url' => $url,
                    'caption' => "تصویر {$createProdcut->title}",
                ]);
            }
            $random_titles = array_map(function ($product) {
                return $product['attribute_title'];
            }, $random_products);

            $imploded_titles = implode("-", [$createProdcut->title, ...$random_titles]);
            $variant = $createProdcut->variants()->create([
                "sku" => $imploded_titles,
                "price" => generateRandomFakeprice(),
            ]);

            foreach ($random_products as $random_product) {
                $variant->attributes()->create([
                    "attribute_title" => $random_product['attribute_title'],
                    "attribute_value" => $random_product['attribute_value'],
                    "attribute_type" => $random_product['attribute_type']
                ]);
            }


        }


    }
}
