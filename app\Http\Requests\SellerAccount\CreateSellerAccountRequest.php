<?php

namespace App\Http\Requests\SellerAccount;

use Illuminate\Foundation\Http\FormRequest;

class CreateSellerAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'bank_id' => 'required|uuid|exists:supporting_banks,uuid',
            'account_number' => 'required|string|min:13,max:15',
            'card_number' => 'required|string|digits:16',
            'sheba_number' => 'required|string|digits:26',
        ];
    }
}
