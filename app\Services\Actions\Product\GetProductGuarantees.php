<?php

namespace App\Services\Actions\Product;

use Illuminate\Database\Eloquent\Collection;

/**
 * Get Product Guarantees Action
 *
 * Retrieves all guarantees available for a specific product.
 */
class GetProductGuarantees
{
    /**
     * Handle retrieving guarantees for a product.
     *
     * @param array $data [
     *   'product' => Product - The product model instance
     * ]
     * @return Collection
     */
    public function handle(array $data): Collection
    {
        $product = $data['product'];

        // Get all active guarantees for this product using many-to-many relationship
        $guarantees = $product->guarantees()
            ->orderBy('price', 'asc') // Order by price ascending
            ->get();

        return $guarantees;
    }
}
