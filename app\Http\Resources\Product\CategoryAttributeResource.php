<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Category Attribute models into API responses.
 * Returns attribute titles with their values as a simple array of strings.
 */
class CategoryAttributeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'title' => $this->title,
            'english_title' => $this->value,
        ];

        // Include values as a simple array of strings if values are loaded
        if ($this->relationLoaded('values')) {
            $data['values'] = [];
            foreach ($this->values as $value) {
                if ($value['value'] && isStrictJson($value['value']))
                    $hex = array_values(json_decode($value['value'], 1));
                $data['values'][] = [
                    "title" => $value['title'],
                    "value" => isset($hex[0]) ? $hex[0] : null,
                ];
            }

        }
        return $data;
    }
}
