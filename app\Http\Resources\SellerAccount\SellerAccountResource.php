<?php

namespace App\Http\Resources\SellerAccount;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SellerAccountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $bank = $this->bank;

        return [
            'id' => $this->uuid,
            'bank_logo' => $bank->logo->getUrl('banks'),
            'bank_title' => $bank->title,
            'account_number' => $this->account_number,
            'card_number' => $this->card_number,
            'sheba_number' => $this->sheba_number,
            'verified' => $this->verified,
        ];
    }
}
