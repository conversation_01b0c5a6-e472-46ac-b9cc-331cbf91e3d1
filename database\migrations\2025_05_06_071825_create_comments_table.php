<?php

use App\Enums\Product\StatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('comments', function ($table) {
            $table->id();
            $table->morphs('commentable');
            $table->foreignId('user_id')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('comments')->onDelete('cascade');
            $table->string('body');
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            // has_bought is only used when the comment is for a product
            $table->boolean('has_bought')->nullable();
            // rate is only used when the comment is for a product (values from 1 to 5)
            $table->float('rate')->default('5');
            $table->enum('status', StatusEnum::values())->default(StatusEnum::CONFIRMED);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::drop('comments');
    }
};