<?php

namespace App\Http\Controllers\Ticket;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\Ticket\CreateTicketMessageRequest;
use App\Http\Requests\Ticket\GetTicketMessagesRequest;
use App\Http\Resources\Ticket\TicketMessageCollection;
use App\Http\Resources\Ticket\TicketMessageResource;
use App\Models\Ticket\Ticket;
use App\Services\Actions\Ticket\CreateTicketMessage;
use App\Services\Actions\Ticket\GetTicketMessages;
use Illuminate\Http\JsonResponse;

class TicketMessageController extends BaseController
{
    /**
     * gets a ticket messages
     * @param GetTicketMessagesRequest $request
     * @param GetTicketMessages $action
     * @return JsonResponse
     */
    public function index(GetTicketMessagesRequest $request, Ticket $ticket, GetTicketMessages $action): JsonResponse
    {
        $messages = $action->handle(
            [
                ...$request->validated(),
                'ticket' => $ticket
            ],
        );
        return $this->sendResponse(
            new TicketMessageCollection($messages),
            __("messages.ticket.messages_retrieved")
        );
    }


    /**
     * create new ticket messages
     * @param GetTicketMessagesRequest $request
     * @param GetTicketMessages $action
     * @return JsonResponse
     */
    public function store(CreateTicketMessageRequest $request, CreateTicketMessage $action): JsonResponse
    {
        $message = $action->handle($request->validated());

        return $this->sendResponse(
            new TicketMessageResource($message),
            __("messages.ticket.messages_retrieved")
        );
    }
}
