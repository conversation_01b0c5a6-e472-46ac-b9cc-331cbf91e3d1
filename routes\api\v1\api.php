<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/**
 * API Routes for version 1
 *
 * All routes in this group are prefixed with 'api/v1'
 */
Route::prefix('v1')->group(function () {
    /**
     * Private routes (authentication required)
     */
    require __DIR__ . '/private.php';
    /**
     * Public routes (no authentication required)
     */
    require __DIR__ . '/public.php';


});


