<?php

namespace App\Http\Controllers\Api\V1\Private\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\CreateProductVariantRequest;
use App\Http\Resources\Product\ProductVariationResource;
use App\Services\Actions\Product\CreateProductVariant;
use Symfony\Component\HttpFoundation\JsonResponse;

class ProductVariantController extends BaseController
{
    /**
     * Store product attributes
     *
     * @param \App\Http\Requests\Product\CreateProductVariantRequest $request 
     * @param \App\Services\Actions\Product\CreateProductVariant $action The action that handles the Store logic.
     * @return \Illuminate\Http\JsonResponse The response containing the updated product resource.
     */
    public function store(CreateProductVariantRequest $request, CreateProductVariant $action): JsonResponse
    {
        $data = $action->handle($request->validated());

        return $this->sendResponse(
            new ProductVariationResource($data),
            __('messages.product.updated')
        );
    }


}
