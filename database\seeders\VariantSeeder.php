<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

/**
 * Seeder for creating product variants for the cloth product.
 */
class VariantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates variants for the cloth product with:
     * - Combinations of sizes (X, XL) and colors (red, blue)
     * - Random prices
     * - Size and color attributes with appropriate metadata
     */
    public function run(): void
    {

        $products = Product::with([
            'category' => function ($query) {
                $query->has('attributes');
            },
            'category.attributes',
            'variants'
        ])->get();
        foreach ($products as $product) {
            $firstVariation = $product->variants()->first();
            if (!$firstVariation) {
                $this->command->info("Product has no variants.");
                continue;
            }
            $category = $product->category;
            if (!$category)
                continue;
            $attrs = $category->getAllSearchableAttributesRecursively();
            foreach ($attrs as $attr) {
                $attributeTitle = $attr['title'] ?? null;
                $attributeType = $attr['value'] ?? null;

                $values = collect($attr['values'] ?? [])
                    ->filter(fn($val) => isset($val['title']) && trim($val['title']) !== '')
                    ->values();

                if ($values->isEmpty()) {
                    continue;
                }

                $randomValue = $values->random();

                $firstVariation->attributes()->create([
                    'attribute_title' => $attributeTitle,
                    'attribute_type' => $attributeType,
                    'attribute_value' => $randomValue['title'],
                    'extra_data' => $randomValue['extra_data'] ?? null,
                ]);



            }

        }

    }
}

