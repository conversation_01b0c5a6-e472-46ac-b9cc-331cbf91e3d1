<?php

namespace App\Services\Actions\User;

use App\Enums\User\UserNotificationStatusEnum;
use App\Enums\User\UserNotificationTypeEnum;
use App\Models\User\UserNotification;
use Illuminate\Pagination\LengthAwarePaginator;


class GetUserNotifications
{
    /**
     * Create a new class instance.
     */
    public function handle(array $data): LengthAwarePaginator
    {

        $limit = $data['limit'] ?? 10;
        $page = $data['page'] ?? null;
        $read = isset($data['read']) ? UserNotificationStatusEnum::from($data['read']) : null;
        $type = isset($data['type']) ? UserNotificationTypeEnum::fromString($data['type']) : null;

        $userId = auth()->id();

        $query = UserNotification::where('user_id', $userId);

        if ($type != null) {
            $query->where('type', $type);
        }
        
        if ($read === UserNotificationStatusEnum::UNREAD) {
            $query->where('is_read', false);
        }

        if ($read === UserNotificationStatusEnum::READ) {
            $query->where('is_read', true);
        }




        return $query->paginate($limit, ['*'], 'page', $page);
    }
}
