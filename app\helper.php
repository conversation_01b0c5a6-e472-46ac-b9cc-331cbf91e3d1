<?php

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
use hisorange\BrowserDetect\Parser as Browser;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

if (!function_exists('faTOen')) {
    function faTOen($string)
    {
        return strtr($string, ['۰' => '0', '۱' => '1', '۲' => '2', '۳' => '3', '۴' => '4', '۵' => '5', '۶' => '6', '۷' => '7', '۸' => '8', '۹' => '9', '٠' => '0', '١' => '1', '٢' => '2', '٣' => '3', '٤' => '4', '٥' => '5', '٦' => '6', '٧' => '7', '٨' => '8', '٩' => '9']);
    }
}
if (!function_exists('getDateTiem')) {
    function getDateTiem($time)
    {
        return new Verta($time);
    }
}

if (!function_exists('get_time')) {
    function get_time($time)
    {
        // return Verta::persianNumbers($time);
        return $time;
    }
}

if (!function_exists('get_ago')) {
    function get_ago($time)
    {
        return verta($time)->formatDifference();
    }
}

if (!function_exists('dateTimeToday')) {
    function dateTimeToday()
    {
        $dateToday = todays();

        return $dateToday . ' ' . date('H:i', time());
    }
}

if (!function_exists('todays')) {
    function todays($day = null)
    {

        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

            return $v->year . '/' . $month . '/' . $day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

        return $v->year . '/' . $month . '/' . $day;
    }
}

if (!function_exists('toYear')) {
    function toYear()
    {
        $v = verta();
        $v->addYear();
        $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

        return $v->year . '/' . $month . '/' . $day;
    }
}

if (!function_exists('shamsiDate')) {
    function shamsiDate($date, $timezone = 'Asia/Tehran', $format = null)
    {
        $v = new Verta($date);

        // تنظیم منطقه زمانی
        $v->timezone($timezone);

        if (!$format)
            return $v->formatJalaliDatetime();
        return $v->format($format);
    }
}

if (!function_exists('shamsiDateLimit')) {
    function shamsiDateLimit($date, $year = null, $timezone = 'Asia/Tehran')
    {
        $v = new Verta($date);
        $v->timezone($timezone);
        if ($year != null) {
            $v->addYear();
        }
        $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

        return $v->year . '/' . $month . '/' . $day;
    }
}

if (!function_exists('TimeDateLimit')) {
    function TimeDateLimit($date, $timezone = 'Asia/Tehran')
    {
        $v = new Verta($date);
        $v->timezone($timezone);

        return str_pad($v->hour, 2, '0', STR_PAD_LEFT) . ':' .
            str_pad($v->minute, 2, '0', STR_PAD_LEFT) . ':' .
            str_pad($v->second, 2, '0', STR_PAD_LEFT);
    }
}

if (!function_exists('shamsiDateDetails')) {
    function shamsiDateDetails()
    {
        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

            return $v->year . '/' . $month . '/' . $day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

        return $day . ' ' . convertIdtoTextMonth($month) . ' ' . $v->year;
    }
}

if (!function_exists('shamsiDateArray')) {
    function shamsiDateArray()
    {
        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

            return $v->year . '/' . $month . '/' . $day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0' . $v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0' . $v->day : $v->day;

        return ['day' => $day, 'month' => convertIdtoTextMonth($month), 'year' => $v->year];
    }
}

if (!function_exists('CarbonDate')) {
    function CarbonDate($date)
    {
        return Verta::parse($date)->datetime();
        // return $v->jalaliToGregorian();
    }
}

// if (!function_exists('setUserLog')) {
//     function setUserLog($request, $phone = null, $description = null)
//     {
//         \App\Models\UserLog::create([
//             'page' => url()->current(),
//             'previous_page' => url()->previous(),
//             'ip' => $request->ip(),
//             'phone' => $phone,
//             'browser' => Browser::browserName(),
//             'platform' => Browser::platformName(),
//             'device' => Browser::deviceType(),
//             'description' => $description,
//         ]);
//     }
// }

if (!function_exists('fixPhoneNumber')) {
    function fixPhoneNumber($phoneNumber)
    {
        $phoneNumber = str_replace('-', '', $phoneNumber);
        $phoneNumber = faTOen($phoneNumber);
        $phoneNumber = Str::replace(' ', '', $phoneNumber);
        $phoneNumber = Str::replace('+98', '0', $phoneNumber);

        return $phoneNumber;
    }
}

if (!function_exists('maskPhoneNumber')) {
    function maskPhoneNumber($phoneNumber)
    {
        // بررسی طول شماره موبایل
        if (strlen($phoneNumber) == 11) {
            // گرفتن پیش‌شماره و اعداد انتهایی شماره
            $prefix = substr($phoneNumber, 0, 4);
            $suffix = substr($phoneNumber, -3);

            // جایگزینی اعداد وسط با ستاره
            $maskedMiddle = '****';

            // ادغام پیش‌شماره، اعداد وسط و اعداد انتهایی با یکدیگر
            $maskedNumber = $prefix . $maskedMiddle . $suffix;

            return $maskedNumber;
        } else {
            // اگر طول شماره موبایل صحیح نباشد، پیام خطا را برگردان
            return '-';
        }
    }
}

if (!function_exists('formatMoney')) {
    function formatMoney($money)
    {
        return $money != null ? number_format(intval(str_replace([',', '،'], '', $money)), 0, '.', ',') : 0;
    }
}

if (!function_exists('PhoneNumberFix')) {
    function PhoneNumberFix(string $phone)
    {
        // حذف فاصله‌ها و سایر کاراکترهای غیر ضروری
        $phone = preg_replace('/\D/', '', $phone); // حذف همه کاراکترهای غیر عددی

        // اگر شماره با 0 شروع نمی‌شود، به ابتدای آن 0 اضافه کن
        if (!str_starts_with($phone, '0')) {
            $phone = '0' . $phone;
        }

        // اصلاح پیشوندهایی مانند +98, 0098 و 98 به 09
        $phone = preg_replace('/^(?:\+98|0098)(\d{10})$/', '09$1', $phone);

        // اگر شماره از ابتدا با 98 شروع می‌شود، فقط شماره بدون پیشوند
        if (substr($phone, 0, 2) == '98' && strlen($phone) == 12) {
            $phone = '0' . substr($phone, 2);
        }

        return $phone;
    }
    //get rand int
    if (!function_exists('randomNumber')) {
        function randomNumber($length = 20, $int = false)
        {
            $numbers = "0123456789";

            $number = '';

            for ($i = 1; $i <= $length; $i++) {
                if ($i == 1) {
                    $num = $numbers[rand(1, strlen($numbers) - 1)];
                } else {
                    $num = $numbers[rand(0, strlen($numbers) - 1)];
                }

                $number .= $num;
            }

            if ($int) {
                return (integer) $number;
            }

            return (string) $number;
        }
    }

    if (!function_exists('generateRandomFakeprice')) {
        /**
         * @param int $length
         * return random price IRR
         */
        function generateRandomFakeprice($length = 6)
        {
            return (double) (randomNumber(2) . str_repeat("0", $length));
        }
    }

}

if (!function_exists('hasRole')) {
    /**
     * Check if the authenticated user has a specific role
     *
     * @param string $role The role to check for
     * @return bool True if the user has the role, false otherwise
     */
    function hasRole($role)
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->role === $role;
    }
}
if (!function_exists('generateRandomSlug')) {

    function generateRandomSlug($length = 8): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $slug = '';

        for ($i = 0; $i < $length; $i++) {
            $slug .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $slug;
    }
}
if (!function_exists('isStrictJson')) {

    function isStrictJson($string): bool
    {
        $decoded = json_decode($string, true);
        return json_last_error() === JSON_ERROR_NONE && (is_array($decoded) || is_object($decoded));
    }
}