<?php

namespace App\Traits\Transaction;

use Carbon\Carbon;
use <PERSON>k<PERSON><PERSON>ser\Verta\Verta;

/**
 * Transaction Attributes Trait
 *
 * This trait contains all attribute methods for the Transaction model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Transaction
 */
trait TransactionAttributesTrait
{
    /**
     * Get the formatted paid date in Shamsi calendar.
     *
     * @return string|null
     */
    public function getShamsiPaidAtAttribute(): ?string
    {
        if (!$this->paid_at) {
            return null;
        }


       return shamsiDate($this->paid_at);
    }

    /**
     * Get the formatted creation date in Shamsi calendar.
     *
     * @return string
     */
    public function getShamsiCreatedAtAttribute(): string
    {
       return shamsiDate($this->created_at);

    }
    public function getIsWalletPaymentAttribute(): string
    {
        return $this->payment_method == 'wallet';
    }
}
