<?php

namespace Database\Seeders;

use App\Models\User\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;
use Faker\Factory as FactoryFaker;
class UserNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Fetch a few users from the database
        $users = User::all();
        $faker = FactoryFaker::create();
        // Loop through the users and create a notification for each
        foreach ($users as $user) {
            for ($i = 0; $i < 5; $i++) {
                $isRead = $faker->boolean; // Randomly determine if the notification is read

                DB::table('user_notifications')->insert([
                    'user_id' => $user->id,
                    'title' => Faker::sentence(),
                    'body' => Faker::paragraph(),
                    'is_read' => $isRead,
                    'data' => json_encode(['extra' => 'data']),
                    'read_at' => $isRead ? now() : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
