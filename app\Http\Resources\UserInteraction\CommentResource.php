<?php

namespace App\Http\Resources\UserInteraction;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Comment models into API responses.
 *
 * Provides a representation of a comment with its body, rating, user information, creation date, and related data.
 */
class CommentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Comment ID
     * - Comment body
     * - Rating (1-5 scale)
     * - Whether the user has bought the product
     * - Creation date
     * - User information (if available)
     * - Replies (if any)
     * - status 
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->load('user');
        $data = [
            'id' => (string) $this->id,
            'body' => $this->body,
            'rate' => $this->rate,
            'has_bought' => $this->has_bought,
            'created_at' => $this->shamsiDate,
            "status" => $this->status->label(),
        ];

        // Add user information if available
        if ($this->relationLoaded('user') && $this->user) {
            $data['user'] = [
                'full_name' => $this->user->full_name,
            ];
        } else {
            $data['user'] = [
                'full_name' => "ناشناس",
            ];
        }

        // Add replies if available
        if ($this->relationLoaded('replies') && $this->replies->isNotEmpty()) {
            $data['replies'] = CommentResource::collection($this->replies);
        }

        return $data;
    }
}
