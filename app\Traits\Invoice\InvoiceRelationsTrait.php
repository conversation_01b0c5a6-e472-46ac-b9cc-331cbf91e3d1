<?php

namespace App\Traits\Invoice;

use App\Models\Discount\DiscountCode;
use App\Models\Shopping\InvoiceProduct;
use App\Models\Shopping\Transaction;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * Invoice Relations Trait
 *
 * This trait contains all relationship methods for the Invoice model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Invoice
 */
trait InvoiceRelationsTrait
{
    /**
     * Get the user that owns this invoice.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the products associated with this invoice.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function products()
    {
        return $this->hasMany(InvoiceProduct::class);
    }

    /**
     * Get the transactions associated with this invoice.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'payable');
    }

    public function discountCode(): BelongsTo
    {
        return $this->belongsTo(DiscountCode::class);
    }
}
