<?php

namespace App\Services\Actions\Product;

use App\Enums\Product\ProductSortOption;
use App\Models\Product\Product;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Action class to retrieve all products with filtering, sorting, and pagination.
 */
class GetAllProducts
{
    private const DEFAULT_LIMIT = 15;
    private const DEFAULT_PAGE = 1;
    private const DEFAULT_SORT = "newest";

    private array $pipeline = [];

    /**
     * Handle the product listing request with filters and pagination.
     */
    public function handle(array $data): array
    {
        $this->pipeline = [];

        $this->buildSearchStage($data);
        $this->buildBasePipeline();
        $this->buildFilterStages($data);

        $priceStats = $this->calculatePriceStatistics();

        $this->buildPriceRangeFilter($data);

        // Now using enum for type safety
        $sortOption = ProductSortOption::fromString($data['sort'] ?? self::DEFAULT_SORT);
        $this->buildSortingStage($sortOption);

        $this->buildPaginationStage($data);

        $result = $this->executePipeline();

        return $this->formatResponse($result, $data, $priceStats);
    }

    /**
     * Build search stage if search term is provided.
     */
    private function buildSearchStage(array $data): void
    {
        if (!isset($data['search'])) {
            return;
        }

        $searchTerm = $data['search'];
        $this->pipeline[] = [
            '$match' => [
                '$or' => [
                    ['$text' => ['$search' => $searchTerm]],
                    ['title' => ['$regex' => '^' . $searchTerm, '$options' => 'i']],
                ]
            ]
        ];
    }

    /**
     * Build the base aggregation pipeline with all necessary lookups.
     */
    private function buildBasePipeline(): void
    {
        $this->addProductIdStringField();
        $this->addVariantsLookup();
        $this->filterProductsWithValidVariants();
        $this->addRelatedDataLookups();
        $this->addPriceFields();
    }

    /**
     * Add string version of product ID for lookups.
     */
    private function addProductIdStringField(): void
    {
        $this->pipeline[] = [
            '$addFields' => [
                'product_id_string' => ['$toString' => '$id']
            ]
        ];
    }

    /**
     * Lookup product variants.
     */
    private function addVariantsLookup(): void
    {
        $this->pipeline[] = [
            '$lookup' => [
                'from' => 'product_variations',
                'localField' => 'product_id_string',
                'foreignField' => 'product_id',
                'as' => 'variants'
            ]
        ];
    }

    /**
     * Filter products that have at least one variant with price > 0.
     */
    private function filterProductsWithValidVariants(): void
    {
        $this->pipeline[] = [
            '$match' => [
                '$expr' => [
                    '$gt' => [
                        [
                            '$size' => [
                                '$filter' => [
                                    'input' => '$variants',
                                    'as' => 'v',
                                    'cond' => ['$gt' => ['$$v.price', 0]]
                                ]
                            ]
                        ],
                        0
                    ]
                ]
            ]
        ];
    }

    /**
     * Add all related data lookups (images, guarantees, categories, etc.).
     */
    private function addRelatedDataLookups(): void
    {
        $this->addGalleryLookup();
        $this->addGuaranteesLookup();
        $this->addCategoriesLookup();
        $this->addInvoicesLookup();
    }

    /**
     * Lookup gallery images.
     */
    private function addGalleryLookup(): void
    {
        $this->pipeline[] = [
            '$lookup' => [
                'from' => 'galleries',
                'let' => ['productIdStr' => '$product_id_string'],
                'pipeline' => [
                    [
                        '$match' => [
                            '$expr' => [
                                '$and' => [
                                    ['$eq' => ['$imageable_id', '$$productIdStr']],
                                    ['$eq' => ['$imageable_type', 'App\\Models\\Product\\Product']]
                                ]
                            ]
                        ]
                    ]
                ],
                'as' => 'gallery'
            ]
        ];
    }

    /**
     * Lookup product guarantees.
     */
    private function addGuaranteesLookup(): void
    {
        $this->pipeline[] = [
            '$lookup' => [
                'from' => 'guarantees',
                'localField' => 'product_id_string',
                'foreignField' => 'product_id',
                'as' => 'guarantees'
            ]
        ];
    }

    /**
     * Lookup product categories with ObjectId conversion.
     */
    private function addCategoriesLookup(): void
    {
        // Convert category_ids to ObjectId
        $this->pipeline[] = [
            '$addFields' => [
                'category_ids' => [
                    '$map' => [
                        'input' => '$category_ids',
                        'as' => 'catId',
                        'in' => ['$toObjectId' => '$$catId']
                    ]
                ]
            ]
        ];

        // Lookup categories
        $this->pipeline[] = [
            '$lookup' => [
                'from' => 'categories',
                'localField' => 'category_ids',
                'foreignField' => 'id',
                'as' => 'categories'
            ]
        ];
    }

    /**
     * Lookup invoices for sales calculations.
     */
    private function addInvoicesLookup(): void
    {
        // Lookup invoice products
        $this->pipeline[] = [
            '$lookup' => [
                'from' => 'invoice_products',
                'localField' => 'product_id_string',
                'foreignField' => 'product_id',
                'as' => 'invoice_links'
            ]
        ];

        // Convert invoice IDs to ObjectId
        $this->pipeline[] = [
            '$addFields' => [
                'invoice_links' => [
                    '$map' => [
                        'input' => '$invoice_links',
                        'as' => 'link',
                        'in' => [
                            '$mergeObjects' => [
                                '$$link',
                                [
                                    'invoice_object_id' => [
                                        '$toObjectId' => '$$link.invoice_id'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Lookup invoices
        $this->pipeline[] = [
            '$lookup' => [
                'from' => 'invoices',
                'localField' => 'invoice_links.invoice_object_id',
                'foreignField' => 'id',
                'as' => 'invoices'
            ]
        ];
    }




    /**
     * Add min and max price calculations.
     */
    private function addPriceFields(): void
    {
        $priceCalculation = [
            '$map' => [
                'input' => [
                    '$filter' => [
                        'input' => '$variants',
                        'as' => 'v',
                        'cond' => [
                            '$gt' => [
                                ['$ifNull' => ['$$v.sale_price', '$$v.price']],
                                0
                            ]
                        ]
                    ]
                ],
                'as' => 'v',
                'in' => [
                    '$cond' => [
                        ['$gt' => ['$$v.sale_price', 0]],
                        '$$v.sale_price',
                        '$$v.price'
                    ]
                ]
            ]
        ];

        $this->pipeline[] = [
            '$addFields' => [
                'min_price' => ['$min' => $priceCalculation],
                'max_price' => ['$max' => $priceCalculation]
            ]
        ];
    }

    /**
     * Add average rating calculation.
     */
    private function addAverageRatingField(): void
    {
        $this->pipeline[] = [
            '$addFields' => [
                'average_rating' => ['$avg' => '$comments.rate']
            ]
        ];
    }

    /**
     * Build all filter stages.
     */
    private function buildFilterStages(array $data): void
    {
        $this->buildStockFilter($data);
        $this->buildGuaranteeFilter($data);
    }

    /**
     * Filter products that are in stock.
     */
    private function buildStockFilter(array $data): void
    {
        if (empty($data['in_stock_only']) || $data['in_stock_only'] != 'true') {
            return;
        }

        $this->pipeline[] = [
            '$match' => [
                '$expr' => [
                    '$gt' => [
                        [
                            '$size' => [
                                '$filter' => [
                                    'input' => '$variants',
                                    'as' => 'v',
                                    'cond' => [
                                        '$and' => [
                                            [
                                                '$gt' => [
                                                    ['$ifNull' => ['$$v.sale_price', '$$v.price']],
                                                    0
                                                ]
                                            ],
                                            ['$gt' => ['$$v.stock', 0]]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        0
                    ]
                ]
            ]
        ];
    }

    /**
     * Filter products that have guarantees.
     */
    private function buildGuaranteeFilter(array $data): void
    {
        if (empty($data['has_guarantee_only']) || $data['has_guarantee_only'] != 'true') {
            return;
        }

        $this->pipeline[] = [
            '$match' => [
                '$expr' => [
                    '$gt' => [
                        ['$size' => ['$ifNull' => ['$guarantees', []]]],
                        0
                    ]
                ]
            ]
        ];
    }

    /**
     * Calculate price statistics for the entire dataset.
     */
    private function calculatePriceStatistics(): array
    {
        $statsPipeline = array_merge($this->pipeline, [
            [
                '$group' => [
                    'id' => null,
                    'min_price' => ['$min' => '$min_price'],
                    'max_price' => ['$max' => '$max_price']
                ]
            ]
        ]);

        $result = Product::raw(function ($collection) use ($statsPipeline) {
            return $collection->aggregate($statsPipeline);
        });

        if ($result && count($result) > 0) {
            return [
                'min_price' => $result[0]['min_price'] ?? 0,
                'max_price' => $result[0]['max_price'] ?? 0
            ];
        }

        return ['min_price' => 0, 'max_price' => 0];
    }

    /**
     * Build price range filter.
     */
    private function buildPriceRangeFilter(array $data): void
    {
        if (!isset($data['min_price']) && !isset($data['max_price'])) {
            return;
        }

        $matchCondition = [];

        if (isset($data['min_price'])) {
            $matchCondition['$gte'] = (float) $data['min_price'];
        }

        if (isset($data['max_price'])) {
            $matchCondition['$lte'] = (float) $data['max_price'];
        }

        if (!empty($matchCondition)) {
            $this->pipeline[] = [
                '$match' => ['min_price' => $matchCondition]
            ];
        }
    }

    /**
     * Build sorting stage based on sort option enum.
     */
    private function buildSortingStage(ProductSortOption $sortOption): void
    {
        match ($sortOption) {
            ProductSortOption::CHEAPEST =>
            $this->pipeline[] = ['$sort' => ['min_price' => 1]],

            ProductSortOption::MOST_EXPENSIVE =>
            $this->pipeline[] = ['$sort' => ['max_price' => -1]],

            ProductSortOption::MOST_SALES => $this->buildMostSalesSort(),

            ProductSortOption::MOST_POPULAR =>
            $this->pipeline[] = ['$sort' => ['average_rating' => -1]],

            ProductSortOption::NEWEST =>
            $this->pipeline[] = ['$sort' => ['created_at' => -1]],
        };
    }

    /**
     * Build most sales sorting with calculation.
     */
    private function buildMostSalesSort(): void
    {
        $this->addSalesCalculation();
        $this->pipeline[] = ['$sort' => ['total_sales' => -1]];
    }

    /**
     * Add sales calculation for most sales sorting.
     */
    private function addSalesCalculation(): void
    {
        $this->pipeline[] = [
            '$addFields' => [
                'total_sales' => [
                    '$sum' => [
                        '$map' => [
                            'input' => [
                                '$filter' => [
                                    'input' => '$invoice_links',
                                    'as' => 'link',
                                    'cond' => [
                                        '$in' => [
                                            '$$link.invoice_object_id',
                                            [
                                                '$map' => [
                                                    'input' => [
                                                        '$filter' => [
                                                            'input' => '$invoices',
                                                            'as' => 'inv',
                                                            'cond' => ['$eq' => ['$$inv.status', 'paid']]
                                                        ]
                                                    ],
                                                    'as' => 'invPaid',
                                                    'in' => '$$invPaid.id'
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            'as' => 'paidLink',
                            'in' => '$$paidLink.quantity'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Build pagination stage.
     */
    private function buildPaginationStage(array $data): void
    {
        $limit = $data['limit'] ?? self::DEFAULT_LIMIT;
        $page = $data['page'] ?? self::DEFAULT_PAGE;
        $skip = $limit * ($page - 1);

        $this->pipeline[] = [
            '$facet' => [
                'data' => [
                    ['$skip' => $skip],
                    ['$limit' => (int) $limit]
                ],
                'count' => [
                    ['$count' => 'total']
                ]
            ]
        ];
    }

    /**
     * Execute the aggregation pipeline.
     */
    private function executePipeline(): Product
    {
        return Product::raw(function ($collection) {
            return $collection->aggregate($this->pipeline);
        })->first();
    }

    /**
     * Format the final response.
     */
    private function formatResponse(Product $result, array $data, array $priceStats): array
    {
        $products = $result['data'] ?? [];
        $totalCount = $result['count'][0]['total'] ?? 0;
        $limit = $data['limit'] ?? self::DEFAULT_LIMIT;
        $page = $data['page'] ?? self::DEFAULT_PAGE;

        $paginator = new LengthAwarePaginator(
            $products,
            $totalCount,
            $limit,
            $page
        );

        return [
            'products' => $paginator,
            'min_price' => $priceStats['min_price'],
            'max_price' => $priceStats['max_price'],
        ];
    }
}