<?php

namespace App\Http\Controllers\Ticket;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\Ticket\CreateTicketRequest;
use App\Http\Requests\Ticket\GetAllTicketsRequest;
use App\Http\Resources\Ticket\TicketCollection;
use App\Http\Resources\Ticket\TicketResource;
use App\Services\Actions\Ticket\CreateTicket;
use App\Services\Actions\Ticket\GetAllTickets;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TicketController extends BaseController
{
    /**
     * gets all tickets related to user
     * @param GetAllTicketsRequest $request
     * @param GetAllTickets $action
     * @return JsonResponse
     */
    public function index(GetAllTicketsRequest $request, GetAllTickets $action): JsonResponse
    {
        $tickets = $action->handle($request->validated());
        return $this->sendResponse(
            new TicketCollection($tickets),
            __("messages.ticket.retrieved")
        );
    }

    /**
     * creates a new ticket for related user
     * @param CreateTicketRequest $request
     * @param CreateTicket $action
     * @return JsonResponse
     */
    public function store(CreateTicketRequest $request, CreateTicket $action): JsonResponse
    {
        $ticket = $action->handle($request->validated(), 'seller');
        return $this->sendResponse(
            new TicketResource($ticket),
            __("messages.ticket.created")
        );
    }
}
