<?php

namespace Database\Factories\Ticket;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use RuntimeException;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ticket\TicketMessage>
 */
class TicketMessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::inRandomOrder()->first();
        return [
            'user_id' => $user->id,
            'message' => Faker::paragraph(),
            'is_admin' => $this->faker->boolean(30),
            'read_at' => $this->faker->boolean(70) ? now() : null,
        ];
    }
}
