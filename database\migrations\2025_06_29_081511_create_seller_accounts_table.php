<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seller_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('bank_id')->constrained('supporting_banks')->onDelete('cascade');
            $table->uuid('uuid')->default(DB::raw('uuid_generate_v4()'))->unique();
            $table->string('account_number');
            $table->string('card_number');
            $table->string('sheba_number');
            $table->boolean('verified')->default(false);
            $table->timestamps();

            $table->unique(['user_id', 'bank_id', 'account_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seller_accounts');
    }
};
