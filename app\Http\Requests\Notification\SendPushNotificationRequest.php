<?php

namespace App\Http\Requests\Notification;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

/**
 * Send Push Notification Request
 *
 * Validates the request data for sending a push notification.
 */
class SendPushNotificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so anyone can access it
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'target' => 'required|string',
            'type' => 'in:token,topic',
            'title' => 'required|string',
            'body' => 'required|string',
            'image' => 'nullable|string',
            'payload' => 'nullable|array',
            'time_to_send' => [
                'required',
                function ($attribute, $value, $fail) {
                    try {
                        $tehran = Carbon::parse($value, 'Asia/Tehran');
                        if ($tehran->clone()->utc()->lte(now()->utc())) {
                            $fail(__('messages.notification.future_time_required'));
                        }
                    } catch (\Exception) {
                        $fail(__('messages.notification.invalid_time_format'));
                    }
                }
            ],
        ];
    }

    // Custom validation messages are now handled by lang/fa/validation.php
}
