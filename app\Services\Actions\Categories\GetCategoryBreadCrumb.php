<?php

namespace App\Services\Actions\Categories;

use Illuminate\Database\Eloquent\Collection;
use App\Models\Product\Category;
/**
 * Action class to retrieve category breadcrumb trail
 */
class GetCategoryBreadCrumb
{
    /**
     * Retrieve breadcrumb trail for a given category
     *
     * @param Category $category The category to build breadcrumb for
     * @return Collection Collection of categories from root to current
     */
    public function handle(Category $category): Collection
    {
        return $this->buildBreadcrumbTrail($category);
    }

    /**
     * Build the breadcrumb trail by traversing up the category hierarchy
     *
     * @param Category $category Starting category
     * @return Collection Breadcrumb trail from root to current category
     */
    private function buildBreadcrumbTrail(Category $category): Collection
    {
        $breadcrumb = new Collection();
        $current = $category;

        // Traverse up the hierarchy to collect all parents
        while ($current != null) {
            $breadcrumb->prepend($current);
            $current = $current->parent;
        }

        return $breadcrumb;
    }

    /**
     * Alternative implementation using recursion
     *
     * @param Category $category Starting category
     * @return Collection Breadcrumb trail from root to current category
     */
    private function buildBreadcrumbTrailRecursive(Category $category): Collection
    {
        if ($category->parent != null) {
            return new Collection([$category]);
        }

        return $this->buildBreadcrumbTrailRecursive($category->parent)
            ->push($category);
    }
}