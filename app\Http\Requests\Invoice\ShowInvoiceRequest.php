<?php

namespace App\Http\Requests\Invoice;

use App\Models\Shopping\Invoice;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Show Invoice Request
 *
 * Validates the request data for retrieving a specific invoice.
 */
class ShowInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can view their invoices
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'invoice_id' => [
                'nullable',
                'string',
                'exists:invoices,id',
                function ($attribute, $value, $fail) {
                    // Find the invoice
                    $invoice = Invoice::find($value);

                    // Check if the invoice belongs to the user
                    if ($invoice && $invoice->user_id != auth()->id()) {
                        $fail(__('messages.invoice.not_owned'));
                    }
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'invoice_id.required' => __('messages.invoice.id_required'),
            'invoice_id.exists' => __('messages.invoice.not_found'),
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Extract invoice_id from route parameters and add it to the request data
        $this->merge([
            'invoice_id' => $this->route('invoice_id'),
        ]);
    }
}
