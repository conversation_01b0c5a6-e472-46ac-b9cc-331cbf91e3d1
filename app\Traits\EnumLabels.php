<?php
namespace App\Traits;

trait EnumLabels
{
    /**
     * List of snake-case labels for *all* enum cases.
     *
     * @return array<string>
     */
    public static function labels(): array
    {
        return array_map(
            fn(self $case) => strtolower($case->name),
            self::cases()
        );
    }
    public function label(): string
    {
        return strtolower($this->name);
    }

}
