<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User\User;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

/**
 * Seeder for creating users in the database.
 */
class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates 10 users with:
     * - Persian first and last names
     * - Properly formatted email addresses
     * - Alternating buyer/seller roles
     * - Standard Iranian mobile phone numbers
     * - Default password: 'password123'
     */
    public function run(): void
    {
        // Create 10 users with Persian names
        for ($i = 0; $i < 10; $i++) {
            $firstName = Faker::firstName();
            $lastName = Faker::lastName();
            $fullName = $firstName . ' ' . $lastName;

            // Generate a more appropriate email for Persian names
            // Use a random English username instead of transliterating Persian names
            $emailPrefix = strtolower('user' . ($i + 1) . rand(100, 999));
            $email = $emailPrefix . '@example.com';

            $role = $i % 2 === 0 ? 'buyer' : 'seller';

            // Use the Persian Faker mobile method for phone numbers
            // This will generate a standard Iranian mobile number format
            $phone = Faker::mobile();

            User::updateOrCreate(
                ['email' => $email], // search by email (unique)
                [
                    'phone' => $phone,
                    'password' => Hash::make('password123'),
                    'full_name' => $fullName,
                    'role' => $role,
                ]
            );
        }
    }
}
