<?php

namespace App\Http\Resources\Discount;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TotalAmountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'subtotal' => $this['subtotal'],
            'total_discount' => $this['total_discount'],
            'total' => $this['total'],
            'off_amount' => $this['off_amount'],
        ];
    }
}
