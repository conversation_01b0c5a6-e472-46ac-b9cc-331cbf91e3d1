<?php

namespace App\Services\Actions\Product\Favorite;

use App\Enums\Product\ProductSortOption;
use App\Traits\Products\ProductQueryTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;


class GetUserFavoriteProducts
{
    private const DEFAULT_LIMIT = 15;
    private const DEFAULT_PAGE = 1;
    private const DEFAULT_SORT = "newest";
    use ProductQueryTrait;
    /**
     * Gets All user favorite products
     * @param array $data
     * @return LengthAwarePaginator
     */
    public function handle(array $data): LengthAwarePaginator
    {


        $sort = ProductSortOption::fromString($data['sort'] ?? self::DEFAULT_SORT);
        $user = auth()->user();
        $query = $user->favoriteProducts()->getQuery();
        $this->applySorting($query, $sort);
        return $this->applyPagination($query, $data);


    }

    protected function applySorting(Builder $query, ProductSortOption $sort): void
    {
        switch ($sort) {
            case ProductSortOption::NEWEST:
                $query->orderByDesc('product_favorites.created_at');
                break;
            case ProductSortOption::CHEAPEST:
                $query->orderBy('min_price');
                break;
            case ProductSortOption::MOST_EXPENSIVE:
                $query->orderByDesc('max_price');
                break;
            case ProductSortOption::MOST_SALES:
                $query->orderByDesc('total_sales');
                break;
            case ProductSortOption::MOST_POPULAR:
                $query->orderByDesc('rate');
                break;
            default:
                $query->orderByDesc('product_favorites.created_at');
                break;
        }
    }

}
