<?php

namespace Database\Seeders;

use App\Models\Product\Attribute;
use App\Models\Product\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Seeder for creating product attributes and their values.
 *
 * Creates parent attributes (titles) and child attributes (values)
 * and associates them with existing categories.
 */
class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates parent attributes with their child values and associates
     * them with existing categories from the CategorySeeder.
     */
    public function run(): void
    {
        $this->createAttributes();


    }

    /**
     * Create all attributes with their values and category associations.
     */
    private function createAttributes(): void
    {
        // Get some existing categories to associate with attributes
        $categories = Category::limit(5)->get();

        if ($categories->isEmpty()) {
            $this->command->warn('No categories found. Make sure to run CategorySeeder first.');
            return;
        }

        // Define attributes with their properties and values
        $attributesData = [
            [
                'title' => 'رنگ',
                'value' => 'color',
                'searchable' => true,
                'values' => ['قرمز', 'آبی', 'سبز', 'مشکی', 'سفید'],
                'ext' => ['#FF0000', '#0000FF', '#008000', '#000000', '#ffffff'],
            ],
            [
                'title' => 'سایز',
                'value' => 'size',

                'searchable' => true,
                'values' => ['کوچک', 'متوسط', 'بزرگ', 'XL', 'XXL']
            ],
            [
                'title' => 'برند',
                'value' => 'brand',

                'searchable' => true,
                'values' => ['سامسونگ', 'اپل', 'ال جی']
            ],
            [
                'title' => 'جنس',
                'value' => 'jens',

                'searchable' => false,
                'values' => ['پنبه', 'پلی استر', 'چرم']
            ],
            [
                'title' => 'وزن',
                'value' => 'weight',

                'searchable' => false,
                'values' => ['سبک', 'متوسط', 'سنگین']
            ]
        ];

        foreach ($attributesData as $attributeData) {
            $this->createAttributeWithValues($attributeData, $categories);
        }
    }

    /**
     * Create a parent attribute with its child values.
     *
     * @param array $attributeData
     * @param \Illuminate\Database\Eloquent\Collection $categories
     */
    private function createAttributeWithValues(array $attributeData, $categories): void
    {
        // Randomly select a category for this attribute
        $randomCategory = $categories->random();

        // Create or update the parent attribute
        $parentAttribute = Attribute::updateOrCreate(
            [
                'title' => $attributeData['title'],
                'parent_id' => null
            ],
            [
                'category_id' => $randomCategory->id,
                'searchable' => $attributeData['searchable'],
                'value' => $attributeData['value'],

            ]
        );


        $this->command->info("Created parent attribute: {$attributeData['title']}");

        // Create child attributes (values)
        $counter = 0;
        foreach ($attributeData['values'] as $valueTitle) {
            $extraData = [];
            if (isset($attributeData['ext'])) {
                $extraData["hex"] = $attributeData['ext'][$counter];
            }
            Attribute::updateOrCreate(
                [
                    'title' => $valueTitle,
                    'parent_id' => $parentAttribute->id
                ],
                [
                    'category_id' => $randomCategory->id,
                    'searchable' => false, // Child attributes are not searchable
                    'value' => json_encode($extraData)
                ]
            );

        }

        $this->command->info("Created " . count($attributeData['values']) . " values for {$attributeData['title']}");
    }
}
