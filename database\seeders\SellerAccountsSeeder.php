<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SellerAccounting\SellerAccount;
use App\Models\User\User;
use App\Models\SellerAccounting\SupportingBank;

class SellerAccountsSeeder extends Seeder
{
    public function run(): void
    {

        $users = User::all();
        $banks = SupportingBank::pluck('id');

        $faker = fake();

        foreach ($users as $user) {
            // Each user gets one random seller account.
            SellerAccount::create([
                'user_id' => $user->id,
                'bank_id' => $banks->random(),
                'account_number' => $faker->bankAccountNumber,
                'card_number' => $faker->creditCardNumber,
                'sheba_number' => 'IR' . $faker->numerify(str_repeat('#', 24)),
                'verified' => $faker->boolean(70),
            ]);
        }
    }
}
