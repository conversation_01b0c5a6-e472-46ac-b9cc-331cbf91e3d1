<?php

namespace App\Http\Requests\Notification;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Store Client Token Request
 *
 * Validates the request data for storing a Firebase client token.
 */
class StoreClientTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so anyone can access it
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'path' => 'nullable|string',
            'token' => 'required|string',
        ];
    }

    // Custom validation messages are now handled by lang/fa/validation.php

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Add IP and User Agent to the validated data
        $this->merge([
            'ip' => $this->ip(),
            'agent' => $this->userAgent(),
        ]);
    }
}
