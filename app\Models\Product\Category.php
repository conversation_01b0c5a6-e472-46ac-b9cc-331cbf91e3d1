<?php


namespace App\Models\Product;

use App\Traits\Category\CategoryRelationsTrait;
use App\Traits\Category\CategoryAttributesTrait;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use CategoryRelationsTrait, CategoryAttributesTrait;
    
    

    protected $fillable = ['title', 'slug', 'parent_id', 'type'];

    protected $casts = [
        'parent_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

}
