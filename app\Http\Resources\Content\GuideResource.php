<?php

namespace App\Http\Resources\Content;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Guide models into API responses.
 *
 * Provides a representation of a guide with its title, content, and order.
 */
class GuideResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Guide ID
     * - Title
     * - Content (HTML)
     * - Order (for sorting)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->id,
            'title' => $this->title,
            'content' => $this->content,
            'order' => $this->order,
        ];
    }
}
