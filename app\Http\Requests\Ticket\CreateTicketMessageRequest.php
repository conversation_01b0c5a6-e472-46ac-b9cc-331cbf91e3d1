<?php

namespace App\Http\Requests\Ticket;
use Illuminate\Foundation\Http\FormRequest;

class CreateTicketMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

            'ticket_id' => [
                'required',
                'integer',
                'exists:tickets,id',
            ],

            'message' => [
                'required',
                'string',
                'max:500',
            ],

            'image' => [
                'sometimes',
                'image',
                'max:5120'
            ]
        ];
    }
}
