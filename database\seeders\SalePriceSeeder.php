<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\ProductVariant;

/**
 * Seeder for adding sale prices to product variants.
 */
class SalePriceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Adds sale prices to some product variants.
     */
    public function run(): void
    {
        // Get all product variants
        $variants = ProductVariant::all();

        // Add sale prices to 50% of variants
        foreach ($variants as $index => $variation) {
            // Only add sale prices to every other variation
            if ($index % 2 === 0) {
                // Set sale price to 10-30% off the regular price
                $discountPercentage = rand(10, 30);
                $salePrice = (int)($variation->price * (1 - $discountPercentage / 100));
                
                $variation->update([
                    'sale_price' => $salePrice
                ]);
                
                $this->command->info("Added sale price of {$salePrice} to variation {$variation->sku} (original price: {$variation->price})");
            }
        }
        
        $this->command->info('Sale prices added successfully!');
    }
}
