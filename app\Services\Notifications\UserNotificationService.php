<?php

namespace App\Services\Notifications;

use App\Models\Shopping\Invoice;
use App\Models\Shopping\Transaction;
use App\Models\Product\ProductsReservation;
use App\Models\Notification\FirebaseToken;
use App\Services\Actions\Notification\SendPushNotification;
use Illuminate\Support\Facades\Log;

/**
 * User Notification Service
 *
 * Handles sending push notifications to users for various marketplace events.
 * Follows clean architecture pattern by providing a dedicated service layer
 * for notification logic separate from business actions.
 */
class UserNotificationService
{
    public function __construct(
        private SendPushNotification $pushNotificationAction
    ) {}

    /**
     * Send notification when a new invoice is created
     *
     * @param Invoice $invoice
     * @return void
     */
    public function sendInvoiceCreated(Invoice $invoice): void
    {
        $tokens = $this->getUserFirebaseTokens($invoice->user_id);
        
        if (empty($tokens)) {
            Log::info('No Firebase tokens found for user', ['user_id' => $invoice->user_id]);
            return;
        }

        foreach ($tokens as $token) {
            $this->pushNotificationAction->handle([
                'title' => __('messages.notifications.invoice_created_title'),
                'body' => __('messages.notifications.invoice_created_body', ['number' => $invoice->invoice_number]),
                'type' => 'token',
                'target' => $token,
                'time_to_send' => now(),
                'payload' => [
                    'type' => 'invoice_created',
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'total' => $invoice->total
                ]
            ]);
        }

        Log::info('Invoice created notification sent', [
            'invoice_id' => $invoice->id,
            'user_id' => $invoice->user_id,
            'tokens_count' => count($tokens)
        ]);
    }

    /**
     * Send notification when invoice payment is successful
     *
     * @param Invoice $invoice
     * @param Transaction $transaction
     * @return void
     */
    public function sendInvoicePaymentSuccess(Invoice $invoice, Transaction $transaction): void
    {
        $tokens = $this->getUserFirebaseTokens($invoice->user_id);
        
        if (empty($tokens)) {
            Log::info('No Firebase tokens found for user', ['user_id' => $invoice->user_id]);
            return;
        }

        foreach ($tokens as $token) {
            $this->pushNotificationAction->handle([
                'title' => __('messages.notifications.payment_success_title'),
                'body' => __('messages.notifications.payment_success_body', [
                    'number' => $invoice->invoice_number,
                    'amount' => number_format($transaction->amount)
                ]),
                'type' => 'token',
                'target' => $token,
                'time_to_send' => now(),
                'payload' => [
                    'type' => 'payment_success',
                    'invoice_id' => $invoice->id,
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'payment_method' => $transaction->payment_method
                ]
            ]);
        }

        Log::info('Payment success notification sent', [
            'invoice_id' => $invoice->id,
            'transaction_id' => $transaction->id,
            'user_id' => $invoice->user_id,
            'tokens_count' => count($tokens)
        ]);
    }

    /**
     * Send notification when wallet top-up is successful
     *
     * @param Transaction $transaction
     * @return void
     */
    public function sendWalletTopupSuccess(Transaction $transaction): void
    {
        $tokens = $this->getUserFirebaseTokens($transaction->user_id);
        
        if (empty($tokens)) {
            Log::info('No Firebase tokens found for user', ['user_id' => $transaction->user_id]);
            return;
        }

        foreach ($tokens as $token) {
            $this->pushNotificationAction->handle([
                'title' => __('messages.notifications.wallet_topup_title'),
                'body' => __('messages.notifications.wallet_topup_body', [
                    'amount' => number_format($transaction->amount)
                ]),
                'type' => 'token',
                'target' => $token,
                'time_to_send' => now(),
                'payload' => [
                    'type' => 'wallet_topup',
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount
                ]
            ]);
        }

        Log::info('Wallet topup notification sent', [
            'transaction_id' => $transaction->id,
            'user_id' => $transaction->user_id,
            'tokens_count' => count($tokens)
        ]);
    }

    /**
     * Send notification when delivery status changes
     *
     * @param Invoice $invoice
     * @param string $oldStatus
     * @param string $newStatus
     * @return void
     */
    public function sendDeliveryStatusUpdate(Invoice $invoice, string $oldStatus, string $newStatus): void
    {
        $tokens = $this->getUserFirebaseTokens($invoice->user_id);
        
        if (empty($tokens)) {
            Log::info('No Firebase tokens found for user', ['user_id' => $invoice->user_id]);
            return;
        }

        foreach ($tokens as $token) {
            $this->pushNotificationAction->handle([
                'title' => __('messages.notifications.delivery_status_title'),
                'body' => __('messages.notifications.delivery_status_body', [
                    'number' => $invoice->invoice_number,
                    'status' => __('messages.delivery_status.' . $newStatus)
                ]),
                'type' => 'token',
                'target' => $token,
                'time_to_send' => now(),
                'payload' => [
                    'type' => 'delivery_status_update',
                    'invoice_id' => $invoice->id,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus
                ]
            ]);
        }

        Log::info('Delivery status notification sent', [
            'invoice_id' => $invoice->id,
            'user_id' => $invoice->user_id,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'tokens_count' => count($tokens)
        ]);
    }

    /**
     * Send notification when product reservation expires
     *
     * @param ProductsReservation $reservation
     * @return void
     */
    public function sendReservationExpired(ProductsReservation $reservation): void
    {
        $tokens = $this->getUserFirebaseTokens($reservation->user_id);
        
        if (empty($tokens)) {
            Log::info('No Firebase tokens found for user', ['user_id' => $reservation->user_id]);
            return;
        }

        foreach ($tokens as $token) {
            $this->pushNotificationAction->handle([
                'title' => __('messages.notifications.reservation_expired_title'),
                'body' => __('messages.notifications.reservation_expired_body'),
                'type' => 'token',
                'target' => $token,
                'time_to_send' => now(),
                'payload' => [
                    'type' => 'reservation_expired',
                    'reservation_id' => $reservation->id,
                    'product_variant_id' => $reservation->product_variant_id,
                    'quantity' => $reservation->quantity
                ]
            ]);
        }

        Log::info('Reservation expired notification sent', [
            'reservation_id' => $reservation->id,
            'user_id' => $reservation->user_id,
            'tokens_count' => count($tokens)
        ]);
    }

    /**
     * Get all Firebase tokens for a specific user
     * 
     * Note: Currently Firebase tokens don't have user_id association.
     * This method will need to be updated once user association is implemented.
     *
     * @param string $userId
     * @return array
     */
    private function getUserFirebaseTokens(string $userId): array
    {
        // TODO: This needs to be implemented once user_id is added back to firebase_tokens table
        // For now, we'll return an empty array and log the issue
        Log::warning('Firebase tokens are not associated with users yet', [
            'user_id' => $userId,
            'message' => 'Need to implement user association for Firebase tokens'
        ]);
        
        return [];
        
        // This is what the implementation should look like once user_id is added:
        // return FirebaseToken::where('user_id', $userId)
        //     ->pluck('token')
        //     ->toArray();
    }
}
