<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id');
            $table->string('sku')->unique();
            $table->double('price');
            $table->double('sale_price')->nullable();
            $table->integer('stock')->default(0);
            $table->timestamps();
        });

        Schema::create('variation_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_variant_id');
            $table->string('attribute_title');
            $table->string('attribute_value');
            $table->string('attribute_type');
            $table->json('extra_data')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('variants');
        Schema::dropIfExists('variation_attributes');
    }
};
