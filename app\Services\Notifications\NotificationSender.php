<?php

namespace App\Services\Notifications;

use App\Services\Notifications\Contracts\NotificationDriverInterface;
use App\Services\Notifications\Drivers\FirebaseNotificationDriver;

/**
 * Service class for sending notifications through various drivers.
 *
 * This class implements a strategy pattern to allow different notification
 * drivers (Firebase, OneSignal, etc.) to be used interchangeably.
 * It also tracks errors that occur during notification sending.
 */
class NotificationSender
{
    /**
     * The notification driver implementation.
     *
     * @var NotificationDriverInterface
     */
    protected NotificationDriverInterface $driver;

    /**
     * Create a new notification sender instance.
     *
     * @param NotificationDriverInterface|null $driver Optional driver implementation
     */
    public function __construct(?NotificationDriverInterface $driver = null)
    {
        $this->driver = $driver ?? $this->resolveDriver();
    }

    /**
     * Stores the last error message if a send operation fails.
     *
     * @var string|null
     */
    protected $lastError = null;

    /**
     * Send a notification using the configured driver.
     *
     * @param array $data Notification data to send
     * @return bool True if the notification was sent successfully
     */
    public function send(array $data): bool
    {
        $result = $this->driver->send($data);

        // If the send failed and we have a notification ID, try to get the error from the driver
        if (!$result && isset($data['notification_id'])) {
            // Check if the notification has an error field that was updated
            $notification = \App\Models\Notification\Notification::find($data['notification_id']);
            if ($notification && $notification->error) {
                $this->lastError = $notification->error;
            }
        }

        return $result;
    }

    /**
     * Get the last error that occurred during a send operation.
     *
     * @return string|null The error message or null if no error occurred
     */
    public function getLastError()
    {
        return $this->lastError;
    }

    /**
     * Set the notification driver to use.
     *
     * @param NotificationDriverInterface $driver The driver to use
     * @return void
     */
    public function setDriver(NotificationDriverInterface $driver): void
    {
        $this->driver = $driver;
    }

    /**
     * Resolve the notification driver based on configuration.
     *
     * @return NotificationDriverInterface The resolved driver
     * @throws \InvalidArgumentException If the configured driver is not supported
     */
    protected function resolveDriver(): NotificationDriverInterface
    {
        return match (config('notifications.driver', 'firebase')) {
            'firebase' => new FirebaseNotificationDriver(),

            // Example for future driver
            // 'onesignal' => new OneSignalNotificationDriver(),

            default => throw new \InvalidArgumentException("Unsupported notification driver"),
        };
    }
}
