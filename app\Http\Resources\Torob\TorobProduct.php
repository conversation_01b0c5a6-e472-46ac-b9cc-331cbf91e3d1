<?php

namespace App\Http\Resources\Torob;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;

class TorobProduct extends JsonResource
{    /**
     * Transform the resource into an array.
     * فیلدهایی که به صورت Optional مشخص شده اند اختیاری بوده و می‌توانند مقدار null داشته باشند ولی در صورت عدم پیاده سازی ممکن است تایید و دسته بندی محصولات با تاخیر انجام شود.
     */
    public function toArray(Request $request): array
    {
        $baseUrl = config("torob.frontend_products_base_url");
        $product = $this->whenLoaded("product");
        return [
            'page_unique' => $this->sku,
            'page_url' => $baseUrl . $product->slug,
            'product_group_id' => $product->id,
            'title' => $product->title,
            'current_price' => $this->sale_price ?? $this->price ?? 0,
            'old_price' => $this->price,
            'availability' => $this->CurrentQuantity > 0,
            // 'category_name' => $product->categories()->pluck('title'),
            'image_links' => $product->gallery()->pluck('image_url'),
            'spec' => $product->details->pluck('value', 'key'),
            'guarantee' => optional($product->guarantees()->first())->company_name,
            'short_desc' => Str::limit($product->description, 500),
            'date_added' => $this->created_at->toIso8601String(),
            'date_updated' => $this->updated_at->toIso8601String(),
            "seller_name" => null,
            "seller_city" => null,
            "subtitle" => null,
            "category_name" => null,
        ];

    }
}
