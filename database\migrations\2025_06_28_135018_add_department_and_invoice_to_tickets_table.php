<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->foreignId('department_id')
                ->nullable()
                ->after('assigned_to_id')
                ->constrained('ticket_departments')
                ->cascadeOnDelete();

            $table->foreignId('invoice_id')
                ->nullable()
                ->after('department_id')
                ->constrained('invoices')
                ->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropForeign(['department_id']);
            $table->dropForeign(['invoice_id']);
            $table->dropColumn(['department_id', 'invoice_id']);
        });
    }
};
