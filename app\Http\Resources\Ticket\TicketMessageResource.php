<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $image = $this->gallery()->first();
        return [
            'id' => $this->id,
            "is_admin" => $this->is_admin,
            "created_at" => $this->shamsiCreatedAt,
            'user_name' => $this->user->full_name,
            'message' => $this->message,
            'attachment' => $image?->getUrl('ticket'),
        ];
    }
}
