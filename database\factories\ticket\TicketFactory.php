<?php

namespace Database\Factories\Ticket;

use App\Enums\Ticket\TicketStatusEnum;
use App\Enums\Ticket\TicketTypeEnum;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;


/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ticket\Ticket>
 */
class TicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $creator = User::inRandomOrder()->first();
        $assignee = null;
        if ($this->faker->boolean(50)) {
            $assignee = User::whereKeyNot($creator->id)->inRandomOrder()->first();
        }
        return [
            'title' => Faker::sentence(),
            'user_id' => $creator->id,
            'assigned_to_id' => $assignee?->id,
            'ticket_status' => fake()->randomElement(TicketStatusEnum::cases())->value,
            'type' => fake()->randomElement(TicketTypeEnum::cases())->value,
        ];
    }
}
