<?php

namespace App\Http\Requests\Ticket;

use App\Enums\Ticket\TicketStatusEnum;
use App\Enums\Ticket\TicketTypeEnum;
use Illuminate\Foundation\Http\FormRequest;

class GetAllTicketsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => [
                'sometimes',
                TicketTypeEnum::rule()
            ],
            'status' => [
                'sometimes',
                TicketStatusEnum::rule()
            ],
            
            'page' => 'sometimes|integer',
            'limit' => 'sometimes|integer|min:1|max:15'
        ];
    }
}
