<?php

namespace App\Traits\Attribute;

/**
 * Attribute Attributes Trait
 *
 * This trait contains all attribute methods for the Attribute model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Attribute
 */
trait AttributeAttributesTrait
{
    /**
     * Check if this is an attribute title (parent attribute).
     *
     * @return bool
     */
    public function getIsParentAttribute(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Check if this is an attribute value (child attribute).
     *
     * @return bool
     */
    public function getIsValueAttribute(): bool
    {
        return !is_null($this->parent_id);
    }
}
