<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Storage;

/**
 * User Resource
 *
 * Transforms User model to a standardized API response
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $this->load('profileImage');
        $profileImage = $this->profileImage()->first();
        return [
            'id' => $this->id,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'shamsi_birth_date' => $this->shamsiBirthDate,
            'national_code' => $this->national_code,
            'profile_image' => $profileImage ? Storage::disk('profile')->url($profileImage->image_path) : null

        ];
    }
}
