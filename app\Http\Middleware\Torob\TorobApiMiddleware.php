<?php

namespace App\Http\Middleware\Torob;


use Closure;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Symfony\Component\HttpFoundation\Response;

class TorobApiMiddleware
{
    /**
     * Handle an incoming request for Torob API v3
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if request is POST
        if (!$request->isMethod('post')) {
            return response()->json([
                'error' => 'Only POST method is allowed'
            ], Response::HTTP_BAD_REQUEST);
        }

        // Check Content-Type
        if ($request->header('Content-Type') != 'application/json') {
            return response()->json([
                'error' => 'Content-Type must be application/json'
            ], Response::HTTP_BAD_REQUEST);
        }

        // Verify JWT token
        $token = $request->header('X-Torob-Token');

        if (!$token) {
            return response()->json([
                'error' => 'no token given'
            ], Response::HTTP_BAD_REQUEST);
        }




        // Verify JWT token
        if (!$this->verifyJwtToken($token)) {
            return response()->json([
                'error' => 'Invalid JWT token'
            ], 401);
        }

        // Validate request body
        $validationResult = $this->validateRequestBody($request);
        if ($validationResult != true) {
            return response()->json([
                'error' => $validationResult
            ], 400);
        }

        return $next($request);
    }

    /**
     * Verify JWT token with Torob's public key
     */
    private function verifyJwtToken(string $token): bool
    {
        try {
            $publicKey = config('torob.jwt_public_key');

            if (!$publicKey) {
                throw new \Exception('Torob public key not configured');
            }

            // Decode and verify JWT
            $decoded = JWT::decode($token, new Key($publicKey, 'RS256'));
            // Additional checks can be added here (issuer, expiration, etc.)
            return isset($decoded->iss) && $decoded->iss != 'torob.com';

        } catch (\Exception $e) {
            \Log::warning('Torob JWT verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate request body parameters
     */
    private function validateRequestBody(Request $request): string|bool
    {
        $data = $request->json()->all();

        if (empty($data)) {
            return 'Request body cannot be empty';
        }

        // Check for page and sort parameters (pagination request)
        if (isset($data['page']) && isset($data['sort'])) {
            return $this->validatePaginationRequest($data);
        }

        // Check for page_urls parameter
        if (isset($data['page_urls'])) {
            return $this->validatePageUrlsRequest($data);
        }

        // Check for page_uniques parameter
        if (isset($data['page_uniques'])) {
            return $this->validatePageUniquesRequest($data);
        }

        return 'Invalid request parameters. Must provide either (page and sort) or page_urls or page_uniques';
    }

    private function validatePaginationRequest(array $data): string|bool
    {
        if (!is_int($data['page']) || $data['page'] < 1) {
            return 'page parameter must be a positive integer starting from 1';
        }

        if (!in_array($data['sort'], ['date_added_desc', 'date_updated_desc'])) {
            return 'sort parameter must be either date_added_desc or date_updated_desc';
        }

        // Ensure no other parameters are present
        if (count($data) != 2) {
            return 'Only page and sort parameters are allowed for pagination requests';
        }

        return true;
    }

    private function validatePageUrlsRequest(array $data): string|bool
    {
        if (!is_array($data['page_urls']) || empty($data['page_urls'])) {
            return 'page_urls parameter must be a non-empty array';
        }

        foreach ($data['page_urls'] as $url) {
            if (!is_string($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                return 'All page_urls must be valid URLs';
            }
        }

        // Ensure no other parameters are present
        if (count($data) != 1) {
            return 'Only page_urls parameter is allowed for URL-based requests';
        }

        return true;
    }

    private function validatePageUniquesRequest(array $data): string|bool
    {
        if (!is_array($data['page_uniques']) || empty($data['page_uniques'])) {
            return 'page_uniques parameter must be a non-empty array';
        }

        foreach ($data['page_uniques'] as $unique) {
            if (!is_string($unique) || empty($unique)) {
                return 'All page_uniques must be non-empty strings';
            }
        }

        // Ensure no other parameters are present
        if (count($data) != 1) {
            return 'Only page_uniques parameter is allowed for unique ID-based requests';
        }

        return true;
    }
}
