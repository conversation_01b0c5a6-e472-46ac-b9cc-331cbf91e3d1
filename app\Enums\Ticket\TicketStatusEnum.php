<?php

namespace App\Enums\Ticket;

use App\Traits\EnumHelpers;
use App\Traits\EnumLabels;
enum TicketStatusEnum: int
{

    use EnumLabels;
    use EnumHelpers;
    case CLOSED = 0;
    case ASSIGNED = 1;
    case IN_PROGRESS = 2;
    case PENDING = 3;
    case RESOLVED = 4;

    public function label(): string
    {
        return match ($this) {
            self::CLOSED => 'closed',
            self::ASSIGNED => 'assigned',
            self::IN_PROGRESS => 'in_progress',
            self::PENDING => 'pending',
            self::RESOLVED => 'resolved',
        };
    }

    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'closed' => self::CLOSED,
            'assigned' => self::ASSIGNED,
            'in_progress' => self::IN_PROGRESS,
            'pending' => self::PENDING,
            'resolved' => self::RESOLVED,
            default => self::PENDING,
        };
    }
}