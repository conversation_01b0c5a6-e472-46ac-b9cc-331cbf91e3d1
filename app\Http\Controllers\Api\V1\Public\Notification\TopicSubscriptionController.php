<?php

namespace App\Http\Controllers\Api\V1\Public\Notification;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Notification\SubscribeToTopic;
use App\Http\Resources\Notification\FirebaseSubscriptionResource;
use App\Http\Requests\Notification\SubscribeToTopicRequest;

/**
 * Controller for managing Firebase topic subscriptions
 *
 * @group Firebase Topic Management
 */
class TopicSubscriptionController extends BaseController
{
    /**
     * Subscribe a Firebase token to a topic
     *
     * @param SubscribeToTopicRequest $request The validated request
     * @param SubscribeToTopic $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function store(SubscribeToTopicRequest $request, SubscribeToTopic $action)
    {
        $response = $action->handle($request->validated());

        return $this->sendResponse(
            new FirebaseSubscriptionResource($response),
            __('messages.notification.topic_subscribed')
        );
    }
}
