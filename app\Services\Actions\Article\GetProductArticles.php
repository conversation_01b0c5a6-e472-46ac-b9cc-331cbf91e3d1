<?php

namespace App\Services\Actions\Article;

use App\Models\Content\Article;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve articles related to a product.
 */
class GetProductArticles
{
    /**
     * Retrieve articles related to a product by product slug.
     *
     * @param array $data An array containing the  key
     * @return Collection Collection of articles
     */
    public function handle(array $data): Collection
    {
        // Find the product by slug - validation is already done in the Form Request
        $product = Product::where('slug', $data[''])->first();

        // Get articles related to this product
        // The morphMany relationship automatically filters by articleable_type and articleable_id
        return $product->articles()->get();
    }
}
