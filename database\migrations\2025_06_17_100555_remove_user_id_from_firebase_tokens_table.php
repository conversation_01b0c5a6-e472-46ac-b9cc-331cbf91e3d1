<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('firebase_tokens', function (Blueprint $table) {
            // safest if you don’t care about keeping the column
            $table->dropColumn('user_id');
        });
}

    public function down(): void
    {
        Schema::table('firebase_tokens', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }
};
