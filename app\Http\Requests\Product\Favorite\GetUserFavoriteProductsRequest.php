<?php

namespace App\Http\Requests\Product\Favorite;

use App\Enums\Product\ProductSortOption;
use Illuminate\Foundation\Http\FormRequest;

class GetUserFavoriteProductsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1|max:100',
            'sort' => ['sometimes', ProductSortOption::rule()],
        ];
    }
}
