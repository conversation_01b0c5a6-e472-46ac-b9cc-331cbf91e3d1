<?xml version="1.0" encoding="UTF-8" ?>
<phpdocumentor
        configVersion="3"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://www.phpdoc.org"
        xsi:noNamespaceSchemaLocation="https://docs.phpdoc.org/latest/phpdoc.xsd"
>
    <paths>
        <output>docs/phpdoc</output>
    </paths>
    <version number="1.0.0">
        <api>
            <source dsn=".">
                <path>app</path>
            </source>
            <ignore>
                <path>app/Http/Middleware/**/*</path>
                <path>app/Providers/**/*</path>
                <path>app/Exceptions/**/*</path>
            </ignore>
            <extensions>
                <extension>php</extension>
            </extensions>
            <default-package-name>Shop</default-package-name>
            <include-source>true</include-source>
        </api>
    </version>
    <template name="default"/>
</phpdocumentor>
