<?php

namespace App\Services\Actions\Comment;

use App\Enums\Product\StatusEnum;
use App\Models\Content\Article;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve comments related to a product.
 */
class GetArticleComments
{

    public function handle(array $data): Collection
    {
        $article = $data['article'];
        // Get top-level comments (where parent_id is null) with their replies and user information
        // Order by creation date (newest first)
        return $article->comments()
            ->whereNull('parent_id')
            ->with(['replies', 'user'])
            ->where('status', StatusEnum::CONFIRMED)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
