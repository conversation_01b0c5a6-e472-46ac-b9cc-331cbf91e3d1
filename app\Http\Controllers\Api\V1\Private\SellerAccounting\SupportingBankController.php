<?php

namespace App\Http\Controllers\Api\V1\Private\SellerAccounting;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\SellerAccount\SupportingBank;
use App\Services\Actions\SellerAccounts\GetSupportingBanks;

class SupportingBankController extends BaseController
{
    /**
     * Gets All supporting banks 
     * @param \App\Services\Actions\SellerAccounts\GetSellerAccounts $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetSupportingBanks $action)
    {
        $banks = $action->handle();
        return $this->sendResponse(
            SupportingBank::collection($banks),
            __('messages.seller_accounts.supporting_banks')
        );
    }

}
