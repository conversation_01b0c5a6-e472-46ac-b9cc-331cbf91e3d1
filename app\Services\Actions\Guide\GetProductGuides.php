<?php

namespace App\Services\Actions\Guide;

use App\Models\Content\Guide;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve guides related to a product.
 */
class GetProductGuides
{
    /**
     * Retrieve guides related to a product by product slug.
     *
     * @param array $data An array containing the  key
     * @return Collection Collection of guides
     */
    public function handle(array $data): Collection
    {
        // Find the product by slug - validation is already done in the Form Request
        $product = Product::where('slug', $data[''])->first();

        // Get guides related to this product, ordered by the order field
        return $product->guides()->orderBy('order')->get();
    }
}
