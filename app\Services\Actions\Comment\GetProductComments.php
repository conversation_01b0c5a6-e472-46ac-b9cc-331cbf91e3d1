<?php

namespace App\Services\Actions\Comment;

use App\Enums\Product\StatusEnum;
use App\Models\UserInteraction\Comment;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve comments related to a product.
 */
class GetProductComments
{
    /**
     * Retrieve comments related to a product by product slug.
     *
     * Only returns top-level comments (not replies) with their replies loaded.
     * Comments are ordered by creation date (newest first).
     *
     * @param array $data An array containing the  key
     * @return Collection Collection of comments
     */
    public function handle(array $data): Collection
    {
        // Find the product by slug - validation is already done in the Form Request
        $product = Product::where('slug', $data[''])->first();

        // Get top-level comments (where parent_id is null) with their replies and user information
        // Order by creation date (newest first)
        return $product->comments()
            ->whereNull('parent_id')
            ->with(['replies', 'user'])
            ->where('status', StatusEnum::CONFIRMED)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
