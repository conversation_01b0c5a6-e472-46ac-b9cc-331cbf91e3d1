<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the invoice_product_details table in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_product_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_product_id'); // FK to invoice_products.id
            $table->string('key'); // Detail key (e.g., 'color', 'size')
            $table->string('value'); // Detail value (e.g., 'red', 'large')

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_product_details');
    }
};
