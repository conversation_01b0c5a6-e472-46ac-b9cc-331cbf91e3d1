<?php

namespace App\Models\Shopping;

use App\Traits\ShoppingCart\ShoppingCartRelationsTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * ShoppingCart Model
 *
 * Represents a shopping cart in the system that belongs to a registered user.
 * Each cart contains multiple cart items stored in a separate collection.
 *
 * @property string $id The unique identifier for the cart
 * @property string $user_id The ID of the user who owns this cart
 * @property \Carbon\Carbon $created_at When the cart was created
 * @property \Carbon\Carbon $updated_at When the cart was last updated
 * @property-read \Illuminate\Database\Eloquent\Collection|CartItem[] $items The items in this cart
 */
class ShoppingCart extends Model
{
    use ShoppingCartRelationsTrait;
    

    protected $fillable = ['user_id'];


}
