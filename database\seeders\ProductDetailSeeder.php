<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;


/**
 * Seeder for creating product details.
 */
class ProductDetailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Adds 3 random details to each product from predefined categories:
     * - Color
     * - Size
     * - Material
     * - Brand
     * - Country of origin
     */
    public function run(): void
    {
        $details = [
            'رنگ' => ['قرمز', 'آبی', 'سبز', 'مشکی', 'سفید'],
            'سایز' => ['کوچک', 'متوسط', 'بزرگ', 'خیلی بزرگ'],
            'جنس' => ['نخی', 'چرم', 'پلی‌استر', 'پشمی'],
            'برند' => ['نایک', 'آدیداس', 'پوما', 'کانورس'],
            'کشور سازنده' => ['ایران', 'چین', 'ترکیه', 'آلمان'],
        ];

        $products = Product::all();

        foreach ($products as $product) {
            // Select 3 random keys from the details array
            foreach (array_rand($details, 3) as $key) {
                // Select a random value for the selected key
                $value = $details[$key][array_rand($details[$key])];

                // Create or update the product detail
                $product->details()->updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }
        }
    }
}
