<?php

namespace App\Services\Notifications\Contracts;

/**
 * Interface for notification drivers.
 *
 * This interface defines the contract that all notification drivers must implement.
 * It allows for different notification services (Firebase, OneSignal, etc.) to be
 * used interchangeably with the NotificationSender service.
 */
interface NotificationDriverInterface
{
    /**
     * Send a notification using the driver's implementation.
     *
     * @param array $data Notification data to send
     * @return bool True if the notification was sent successfully
     */
    public function send(array $data): bool;

    /**
     * Get the last error that occurred during send operation
     *
     * @return string|null The error message or null if no error occurred
     */
    public function getLastError();
}
