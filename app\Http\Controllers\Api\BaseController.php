<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller as Controller;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\App;

class BaseController extends Controller
{
    /**
     * Success response method.
     *
     * @param mixed $data The data to include in the response
     * @param string $message The message key or custom message to include
     * @param int $code The HTTP status code
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendResponse($data, $message = null, $code = Response::HTTP_OK): \Illuminate\Http\JsonResponse
    {


        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'status' => $code,
        ];

        return response()->json($response, $code);
    }

    /**
     * Error response method.
     *
     * @param mixed $errorData The error data to include
     * @param string $message The message key or custom message to include
     * @param int $code The HTTP status code
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendError($errorData = [], $message = null, $code = Response::HTTP_NOT_FOUND)
    {

        $response = [
            'success' => false,
            'message' => $message,
            'status' => $code,
        ];

        if (!empty($errorData)) {
            $response['data'] = $errorData;
        }

        return response()->json($response, $code);
    }
}
