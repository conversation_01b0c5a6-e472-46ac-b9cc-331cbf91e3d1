<?php

namespace App\Models\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Models\Shopping\Invoice;
use App\Models\User\User;
use App\Traits\Models\Helpers\ShamsiCraetedDate;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
class DiscountCode extends Model
{
    use ShamsiCraetedDate;
    protected $fillable = [
        'uuid',
        'code',
        'user_id',
        'type',
        'amount',
        'is_active',
        'expires_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
        'type' => DiscountTypeEnum::class,
    ];

    // Automatically generate UUID on creating
    protected static function booted()
    {
        static::creating(function ($model) {
            if (!$model->uuid) {
                $model->uuid = Str::uuid()->toString();
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function invoice(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function isValid(): bool
    {
        $userId = auth()->id();

        $currectUser = $this->user_id == null || $userId == $this->user_id;
        $notExpired = is_null($this->expires_at) || now()->lessThan($this->expires_at);

        return $currectUser && $this->is_active && $notExpired;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }
    public function scopeWithUser($query)
    {
        return $query->with('user');
    }



    public function ShamsiExpiresAt(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->expires_at ? shamsiDate(date: $this->expires_at) : null,
        );
    }
}
