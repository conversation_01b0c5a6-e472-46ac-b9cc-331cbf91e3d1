<?php

namespace App\Traits\Guide;

use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Guide Relations Trait
 *
 * This trait contains all relationship methods for the Guide model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Guide
 */
trait GuideRelationsTrait
{
    /**
     * Get the parent model that this guide belongs to.
     * This can be any model that uses the morphMany relationship with guides.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function guideable(): MorphTo
    {
        return $this->morphTo();
    }
}
