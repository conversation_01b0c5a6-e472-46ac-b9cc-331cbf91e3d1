<?php

namespace App\Http\Requests\Notification;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Subscribe To Topic Request
 *
 * Validates the request data for subscribing to a Firebase topic.
 */
class SubscribeToTopicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // This is a public endpoint, so anyone can access it
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'token' => 'required|string',
            'topic' => 'required|string',
        ];

    }

    // Custom validation messages are now handled by lang/fa/validation.php
}
