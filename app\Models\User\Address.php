<?php

namespace App\Models\User;

use App\Traits\Address\AddressRelationsTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Address Model
 *
 * Represents a user's address in the system.
 *
 * @property string $id The unique identifier for the address
 * @property string $user_id The ID of the user who owns this address
 * @property string $name The name of the address (e.g., "Home", "Work")
 * @property string $receiver_name The name of the person receiving packages at this address
 * @property string $receiver_phone The phone number of the person receiving packages at this address
 * @property boolean $is_recipient_self Whether the address is for the user themselves or someone else
 * @property string $province The province/state of the address
 * @property string $city The city of the address
 * @property string $zip_code The postal/zip code
 * @property string $address The detailed address
 * @property float $latitude The latitude coordinate of the address
 * @property float $longitude The longitude coordinate of the address
 * @property \Carbon\Carbon $created_at When the address was created
 * @property \Carbon\Carbon $updated_at When the address was last updated
 * @property-read \App\Models\User\User $user The user who owns this address
 */
class Address extends Model
{
    use AddressRelationsTrait;



    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'receiver_name',
        'receiver_phone',
        'is_recipient_self',
        'province',
        'city',
        'zip_code',
        'address',
        'latitude',
        'longitude',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_id' => 'string',
        'is_recipient_self' => 'boolean',
        'latitude' => 'float',
        'longitude' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


}
