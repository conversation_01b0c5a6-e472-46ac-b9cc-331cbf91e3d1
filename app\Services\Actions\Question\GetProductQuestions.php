<?php

namespace App\Services\Actions\Question;

use App\Models\Product\Product;
use App\Models\UserInteraction\Question;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve questions and answers related to a product.
 */
class GetProductQuestions
{
    /**
     * Retrieve questions and their answers related to a product by product slug.
     * Questions are ordered by creation date (newest first).
     *
     * @param array $data An array containing the  key
     * @return Collection Collection of questions
     */
    public function handle(array $data): Collection
    {
        // Find the product by slug - validation is already done in the Form Request
        $product = Product::where('slug', $data[''])->first();

        // Get questions with their answers and user information
        // Order by creation date (newest first)
        return $product->questions()
            ->with(['answers.user', 'user'])
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
