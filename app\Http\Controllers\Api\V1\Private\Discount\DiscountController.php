<?php

namespace App\Http\Controllers\Api\V1\Private\Discount;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Discount\CreateDiscountCodesRequest;
use App\Http\Requests\Discount\GetDiscountCodesRequest;
use App\Http\Resources\Core\PaginatedResourceCollection;
use App\Http\Resources\Discount\DiscountResource;
use App\Services\Actions\Discount\CreateDiscountCode;
use App\Services\Actions\Discount\GetDiscountCodes;
use Symfony\Component\HttpFoundation\JsonResponse;

class DiscountController extends BaseController
{

    /**
     * get all discount codes 
     * @param \App\Http\Requests\Discount\GetDiscountCodesRequest $request
     * @param \App\Services\Actions\Discount\GetDiscountCodes $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetDiscountCodesRequest $request, GetDiscountCodes $action): JsonResponse
    {
        $discountCodes = $action->handle($request->validated());
        return $this->sendResponse(
            new PaginatedResourceCollection(
                $discountCodes,
                'discount_codes',
                DiscountResource::class
            ),
        );
    }
    /**
     * creates new discount code
     * @param \App\Http\Requests\Discount\CreateDiscountCodesRequest $request
     * @param \App\Services\Actions\Discount\CreateDiscountCode $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateDiscountCodesRequest $request, CreateDiscountCode $action): JsonResponse
    {
        $discountCode = $action->handle($request->validated());
        return $this->sendResponse(
            new DiscountResource(
                $discountCode
            ),
        );
    }
}
