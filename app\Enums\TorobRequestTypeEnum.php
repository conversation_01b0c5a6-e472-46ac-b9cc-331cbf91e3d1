<?php

namespace App\Enums;

/**
 * Enum for allowed Torob API request input types
 */
enum TorobRequestTypeEnum: string
{
    case PageUrl = 'page_urls';
    case PageUnique = 'page_uniques';
    case All = 'all';

    /**
     * Check if the given value is a valid TorobRequestType
     */
    public static function isValid(string $value): bool
    {
        return in_array($value, array_column(self::cases(), 'value'));
    }
}