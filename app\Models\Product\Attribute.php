<?php

namespace App\Models\Product;

use App\Traits\Attribute\AttributeRelationsTrait;
use App\Traits\Attribute\AttributeAttributesTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Attribute Model
 *
 * Represents a product attribute in a hierarchical structure.
 * If parent_id is null, it's an attribute title (e.g., size, color).
 * If parent_id is set, it's an attribute value (e.g., red, XL).
 *
 * @property string $id The unique identifier for the attribute
 * @property string|null $parent_id The ID of the parent attribute (null for top-level attributes)
 * @property string $title The title of the attribute (e.g., "رنگ" for color or "قرمز" for red)
 * @property string|null $value Additional value data (e.g., hex code for colors)
 * @property \Carbon\Carbon $created_at When the attribute was created
 * @property \Carbon\Carbon $updated_at When the attribute was last updated
 */
class Attribute extends Model
{
    use AttributeRelationsTrait, AttributeAttributesTrait;

    
    

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'parent_id',
        'category_id',
        'searchable',
        'title',
        'value',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parent_id' => 'string',
        'category_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
