<?php

use App\Enums\Product\StatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Migration for creating the products_reservation table in MongoDB.
 * This table stores temporary reservations of product variants during checkout process.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('invoice_id');
            $table->foreignId('product_variant_id');
            $table->integer('quantity');
            $table->timestamp('expire_date');
            $table->enum('status', StatusEnum::values())->default(StatusEnum::PENDING);
            $table->text('note')->nullable();
            $table->timestamp('restored_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_reservations');
    }
};
