<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryTreeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'categories' => $this->resource,
            'count' => $this->countCategories($this->resource),
            'generated_at' => now()->toISOString()
        ];
    }

    private function countCategories(array $tree): int
    {
        $count = 0;

        foreach ($tree as $key => $value) {
            if (is_string($key)) {
                // Has children
                $count += 1 + $this->countCategories($value);
            } else {
                // Leaf category
                $count++;
            }
        }

        return $count;
    }
}