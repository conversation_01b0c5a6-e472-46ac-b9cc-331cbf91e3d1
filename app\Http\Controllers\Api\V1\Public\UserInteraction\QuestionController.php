<?php

namespace App\Http\Controllers\Api\V1\Public\UserInteraction;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Question\GetProductQuestions;
use App\Http\Resources\UserInteraction\QuestionResource;
use App\Http\Requests\Question\GetProductQuestionsRequest;

/**
 * Controller for retrieving and managing questions and answers
 *
 * @group Question Management
 */
class QuestionController extends BaseController
{
    /**
     * Get questions related to a specific product
     *
     * @param GetProductQuestionsRequest $request The validated request
     * @param string $slug The product slug
     * @param GetProductQuestions $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetProductQuestionsRequest $request, string $slug, GetProductQuestions $action)
    {
        $questions = $action->handle($request->validated());

        return $this->sendResponse(
            QuestionResource::collection($questions),
            __('messages.question.found')
        );
    }
}
