<?php

namespace App\Traits\Products;

use App\Enums\PayTypeEnum;
use App\Enums\Product\ProductSortOption;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Trait for common product query functionality
 */
trait ProductQueryTrait
{
    private const DEFAULT_LIMIT = 15;
    private const DEFAULT_PAGE = 1;
    private const DEFAULT_SORT = "newest";

    /**
     * Build variant conditions based on filters
     */
    protected function buildVariantConditions(array $data): string
    {
        $conditions = 'price > 0';

        if ($this->shouldFilterByStock($data)) {
            $conditions .= ' AND stock > 0';
        }

        return $conditions;
    }

    /**
     * Check if should filter by stock
     */
    protected function shouldFilterByStock(array $data): bool
    {
        return isset($data['in_stock_only']) && $data['in_stock_only'] === 'true';
    }

    /**
     * Check if should filter by guarantee
     */
    protected function shouldFilterByGuarantee(array $data): bool
    {
        return isset($data['has_guarantee_only']) && $data['has_guarantee_only'] === 'true';
    }

    /**
     * Build base product query with common filters
     */
    protected function buildBaseProductQuery(array $data): Builder
    {
        return Product::with([
            'variants' => function ($query) use ($data) {
                $query->where('price', '>', '0')
                    ->when($this->shouldFilterByStock($data), fn($q) => $q->where('stock', '>', 0))
                    ->with('attributes');
            }
        ])
            ->whereHas('variants', function ($subQuery) use ($data) {
                $subQuery->where('price', '>', '0')
                    ->when($this->shouldFilterByStock($data), fn($q) => $q->where('stock', '>', 0));
            })
            ->when($this->shouldFilterByGuarantee($data), fn($q) => $q->whereHas('guarantees'))
            ->when(!empty($data['search']), function ($q) use ($data) {
                $searchTerm = trim($data['search']);
                $q->where(function ($subQ) use ($searchTerm) {
                    $subQ->where('title', 'ILIKE', "%{$searchTerm}%")
                        ->orWhere('description', 'ILIKE', "%{$searchTerm}%");
                });
            });
    }

    /**
     * Calculate overall price range from filtered products
     */
    protected function calculateOverallPriceRange(Builder $baseQuery, string $variantConditions): array
    {
        $priceQuery = clone $baseQuery;
        $priceStats = $priceQuery->selectRaw('
            MIN(
                (SELECT MIN(COALESCE(NULLIF(sale_price, 0), price))
                 FROM product_variants 
                 WHERE products.id = product_variants.product_id 
                 AND ' . $variantConditions . ')
            ) as overall_min_price,
            MAX(
                (SELECT MAX(COALESCE(NULLIF(sale_price, 0), price))
                 FROM product_variants 
                 WHERE products.id = product_variants.product_id 
                 AND ' . $variantConditions . ')
            ) as overall_max_price
        ')->first();

        return [
            'min' => $priceStats->overall_min_price,
            'max' => $priceStats->overall_max_price,
        ];
    }

    /**
     * Add calculated fields to query (min_price, max_price, total_sales)
     */
    protected function addCalculatedFields(Builder $query, string $variantConditions): void
    {
        $paidStatus = PayTypeEnum::PAID->value;

        $query->selectRaw('
            products.*,
            (
                SELECT MIN(COALESCE(NULLIF(sale_price, 0), price))
                FROM product_variants
                WHERE products.id = product_variants.product_id
                AND ' . $variantConditions . '
            ) as min_price,
            (
                SELECT MAX(COALESCE(NULLIF(sale_price, 0), price))
                FROM product_variants
                WHERE products.id = product_variants.product_id
                AND ' . $variantConditions . '
            ) as max_price,
            (
                SELECT COALESCE(SUM(invoice_products.quantity), 0)
                FROM invoice_products
                JOIN invoices ON invoice_products.invoice_id = invoices.id
                WHERE invoice_products.product_id = products.id
                AND invoices.status = ?
            ) as total_sales
        ', [$paidStatus]);
    }

    /**
     * Apply price range filters
     */
    protected function applyPriceFilters(Builder $query, array $data, string $variantConditions): void
    {
        if (isset($data['min_price']) && $data['min_price'] > 0) {
            $query->whereExists(function ($subQuery) use ($data, $variantConditions) {
                $subQuery->select(DB::raw(1))
                    ->from('product_variants')
                    ->whereColumn('products.id', 'product_variants.product_id')
                    ->whereRaw($variantConditions)
                    ->whereRaw('COALESCE(NULLIF(sale_price, 0), price) >= ?', [$data['min_price']]);
            });
        }

        if (isset($data['max_price']) && $data['max_price'] > 0) {
            $query->whereExists(function ($subQuery) use ($data, $variantConditions) {
                $subQuery->select(DB::raw(1))
                    ->from('product_variants')
                    ->whereColumn('products.id', 'product_variants.product_id')
                    ->whereRaw($variantConditions)
                    ->whereRaw('COALESCE(NULLIF(sale_price, 0), price) <= ?', [$data['max_price']]);
            });
        }
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting(Builder $query, ProductSortOption $sort): void
    {
        switch ($sort) {
            case ProductSortOption::NEWEST:
                $query->orderByDesc('created_at');
                break;
            case ProductSortOption::CHEAPEST:
                $query->orderBy('min_price');
                break;
            case ProductSortOption::MOST_EXPENSIVE:
                $query->orderByDesc('max_price');
                break;
            case ProductSortOption::MOST_SALES:
                $query->orderByDesc('total_sales');
                break;
            case ProductSortOption::MOST_POPULAR:
                $query->orderByDesc('rate');
                break;
            default:
                $query->orderByDesc('created_at');
                break;
        }
    }

    /**
     * Apply pagination to query
     */
    protected function applyPagination(Builder $query, array $data)
    {
        $perPage = $data['limit'] ?? self::DEFAULT_LIMIT;
        $page = $data['page'] ?? self::DEFAULT_PAGE;

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Build main query with calculated fields and apply sorting/pagination
     */
    protected function buildMainQuery(Builder $baseQuery, string $variantConditions, array $data, ProductSortOption $sort)
    {
        $query = clone $baseQuery;

        $this->addCalculatedFields($query, $variantConditions);
        $this->applyPriceFilters($query, $data, $variantConditions);
        $this->applySorting($query, $sort);

        return $this->applyPagination($query, $data);
    }

    /**
     * Build complete response with products and price range
     */
    protected function buildProductResponse(Builder $baseQuery, array $data, ProductSortOption $sort): array
    {
        $variantConditions = $this->buildVariantConditions($data);

        // Get overall price range from filtered products
        $priceRange = $this->calculateOverallPriceRange($baseQuery, $variantConditions);

        // Build main query with calculated fields and apply sorting/pagination
        $products = $this->buildMainQuery($baseQuery, $variantConditions, $data, $sort);

        return [
            'products' => $products,
            'min_price' => $priceRange['min'],
            'max_price' => $priceRange['max'],
        ];
    }
}
