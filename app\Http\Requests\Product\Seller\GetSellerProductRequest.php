<?php

namespace App\Http\Requests\Product\Seller;


use App\Enums\Product\ProductSortOption;
use App\Enums\Product\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class GetSellerProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sort'=>['sometimes','string',ProductSortOption::rule()],
            'search'=>'sometimes|string|max:30',
            'shop_id'=>'required|int|exists:shops,id',
        ];
    }


   

}
