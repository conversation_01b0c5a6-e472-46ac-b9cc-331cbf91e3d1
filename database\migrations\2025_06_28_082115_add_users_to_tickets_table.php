<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            // Creator of the ticket (required)
            $table->foreignId('user_id')
                ->after('title')
                ->constrained('users')
                ->cascadeOnDelete();

            // Agent/staff the ticket is assigned to (nullable)
            $table->foreignId('assigned_to_id')
                ->nullable()
                ->after('user_id')
                ->constrained('users')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['assigned_to_id']);
            $table->dropColumn(['user_id', 'assigned_to_id']);
        });
    }
};
