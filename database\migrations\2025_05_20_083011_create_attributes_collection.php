<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the attributes table in MongoDB.
 * This table stores product attributes in a hierarchical structure.
 * - Parent attributes (parent_id = null) represent attribute types like color, size, etc.
 * - Child attributes (with parent_id) represent attribute values like red, XL, etc.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attributes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('attributes')->onDelete('cascade');
            $table->foreignId('category_id');
            $table->string('title');
            $table->string('value')->nullable();
            $table->boolean('searchable')->default(false);
            $table->boolean('priceable')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attributes');
    }
};
