<?php

namespace App\Models\Product;

use App\Traits\ProductDetail\ProductDetailRelationsTrait;
use Illuminate\Database\Eloquent\Model;

class ProductDetail extends Model
{
    use ProductDetailRelationsTrait;
    
    

    protected $fillable = [
        'product_id',
        'key',
        'value',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'product_id' => 'string',
    ];


}
