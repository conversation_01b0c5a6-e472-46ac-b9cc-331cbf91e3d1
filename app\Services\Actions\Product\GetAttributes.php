<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Attribute;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve product attributes.
 */
class GetAttributes
{
    /**
     * Retrieve all parent attributes with their values.
     *
     * @param array $data Optional filter parameters
     * @return Collection Collection of parent attributes with their values
     */
    public function handle(array $data = []): Collection
    {
        // Query for parent attributes (those with null parent_id)
        $query = Attribute::whereNull('parent_id');
        
        // Apply any additional filters if provided
        if (isset($data['title'])) {
            $query->where('title', $data['title']);
        }
        
        // Get the attributes with their values
        return $query->with('values')->get();
    }
}
