<?php

namespace App\Services\Actions\SellerAccounts;

use App\Models\SellerAccounting\SellerAccount;
use Illuminate\Database\Eloquent\Collection;

class GetSellerAccounts
{
    /**
     * Gets All Seller accounts
     * @param array $data
     * @return Collection
     */
    public function handle(): Collection
    {
        $userId = auth()->id();

        $accounts = SellerAccount::with('bank.logo')->where('user_id', $userId)->get();

        return $accounts;
    }
}
