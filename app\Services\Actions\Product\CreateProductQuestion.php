<?php

namespace App\Services\Actions\Product;

use App\Enums\Product\StatusEnum;
use App\Models\Product\Product;
use App\Models\UserInteraction\Question;

class CreateProductQuestion
{
    /**
     * Handle the creation of a new product question.
     *
     * This method locates the product by its slug, prepares question data,
     * assigns the authenticated user ID, sets the default status to 'pending',
     * and creates the question associated with the product.
     *
     * @param array<string, mixed> $data An associative array containing:
     *                                   - 'product_slug': string, the slug of the related product.
     *                                   - other fields for question creation (e.g., 'body').
     * @return Question The newly created question model instance.
     */
    public function handle(array $data): Question
    {

        $product = Product::where('slug', $data['product_slug'])->first();
        $questionData = $data;


        unset($questionData['product_slug']);

        $questionData['user_id'] = auth()->id();
        $questionData['status'] = StatusEnum::PENDING;


        $question = $product->questions()->create($questionData);
        return $question;
    }


}

