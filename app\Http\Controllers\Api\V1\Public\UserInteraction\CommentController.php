<?php

namespace App\Http\Controllers\Api\V1\Public\UserInteraction;

use App\Http\Controllers\Api\BaseController;
use App\Services\Actions\Comment\GetProductComments;
use App\Http\Resources\UserInteraction\CommentResource;
use App\Http\Requests\Comment\GetProductCommentsRequest;

/**
 * Controller for retrieving and managing comments
 *
 * @group Comment Management
 */
class CommentController extends BaseController
{
    /**
     * Get comments related to a specific product
     *
     * @param GetProductCommentsRequest $request The validated request
     * @param string $slug The product slug
     * @param GetProductComments $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetProductCommentsRequest $request, GetProductComments $action)
    {
        $comments = $action->handle($request->validated());

        return $this->sendResponse(
            CommentResource::collection($comments),
            __('messages.comment.found')
        );
    }
}
