<?php

namespace App\Services\Actions\Categories;

use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;



/**
 * Retrieves child categories of a parent by slug
 */
class GetCategoryChildren
{
    /**
     * Retrieves a collection of categories that have a parent with the given slug.
     *
     * This method queries the Category model and returns all categories whose parent category
     * has the specified slug.
     *
     * @param array $data An associative array containing the following key:
     *                    - 'slug' (string): The slug of the parent category.
     *
     * @return \Illuminate\Support\Collection A collection of Category models matching the criteria.
     */
    public function handle(array $data): Collection
    {
        $slug = $data["slug"];
        return Category::whereHas('parent', function ($query) use ($slug) {
            $query->where('slug', $slug);
        })->get();
    }
}