<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up()
    {
            DB::statement("CREATE EXTENSION IF NOT EXISTS pg_trgm");

        // Create GIN indexes for multi-language full-text search
        // Using 'simple' configuration for Persian/Arabic text
        DB::statement("CREATE INDEX products_title_fts_idx ON products USING GIN(to_tsvector('simple', title))");
        DB::statement("CREATE INDEX products_description_fts_idx ON products USING GIN(to_tsvector('simple', description))");
        DB::statement("CREATE INDEX products_combined_fts_idx ON products USING GIN(to_tsvector('simple', coalesce(title, '') || ' ' || coalesce(description, '')))");

        // Also create indexes for case-insensitive LIKE searches as fallback
        DB::statement("CREATE INDEX products_title_ilike_idx ON products USING GIN(title gin_trgm_ops)");
        DB::statement("CREATE INDEX products_description_ilike_idx ON products USING GIN(description gin_trgm_ops)");

        // Enable trigram extension if not already enabled
        // DB::statement("CREATE EXTENSION IF NOT EXISTS pg_trgm");
    }

    public function down()
    {
        DB::statement('DROP INDEX IF EXISTS products_title_fts_idx');
        DB::statement('DROP INDEX IF EXISTS products_description_fts_idx');
        DB::statement('DROP INDEX IF EXISTS products_combined_fts_idx');
        DB::statement('DROP INDEX IF EXISTS products_title_ilike_idx');
        DB::statement('DROP INDEX IF EXISTS products_description_ilike_idx');
    }
};