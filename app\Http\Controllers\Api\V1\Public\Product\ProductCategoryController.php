<?php

namespace App\Http\Controllers\Api\V1\Public\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\GetCategoryProductsRequest;
use App\Http\Resources\Product\CategoryProductIndexResource;
use App\Services\Actions\Product\GetCategoryProducts;
use App\Services\Actions\Categories\GetCategoryProductsSql;




/**
 * Controller for retrieving category information and related data
 *
 * @group Category Management
 */
class ProductCategoryController extends BaseController
{
    /**
     * Display a listing of products for the specified category
     *
     * Returns all products belonging to the given category and its child categories,
     * with support for filtering, sorting, and pagination.
     *
     * @param GetCategoryProductsRequest $request The validated request
     * @param GetCategoryProducts $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetCategoryProductsRequest $request, GetCategoryProductsSql $action)
    {
        $products = $action->handle($request->validated());

        return $this->sendResponse(
            new CategoryProductIndexResource($products),
            __('messages.category.products_found')
        );
    }


}
