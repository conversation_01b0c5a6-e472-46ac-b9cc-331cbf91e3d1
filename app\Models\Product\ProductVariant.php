<?php

namespace App\Models\Product;

use App\Traits\ProductVariant\ProductVariantRelationsTrait;
use App\Traits\ProductVariant\ProductVariantAttributesTrait;
use Illuminate\Database\Eloquent\Model;

class ProductVariant extends Model
{
    use ProductVariantRelationsTrait, ProductVariantAttributesTrait;


    protected $fillable = ['product_id', 'sku', 'price', 'sale_price'];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
    ];


}