<?php

namespace App\Enums\Product;

use App\Traits\EnumHelpers;

enum StatusEnum: int
{

    use EnumHelpers;

    case REJECTED = 0;
    case PENDING = 1;
    case CONFIRMED = 2;


    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'rejected' => self::REJECTED,
            'pending' => self::PENDING,
            'confirmed' => self::CONFIRMED,
            default => self::PENDING,
        };
    }

    public static function toLabel(string|int $value): string
    {
        return match (strtolower($value)) {
            self::REJECTED => 'rejected',
            self::PENDING => 'pending',
            self::CONFIRMED => 'confirmed',
            default => "pending",
        };
    }



    /**
     * Get the string label for the enum case.
     */
    public function label(): string
    {
        return match ($this) {
            self::REJECTED => 'rejected',
            self::PENDING => 'pending',
            self::CONFIRMED => 'confirmed',
        };
    }


    /**
     * Get all possible string labels.
     */
    public static function labels(): array
    {
        return [
            'rejected',
            'pending',
            'confirmed',
        ];
    }



}
