<?php

namespace App\Services\Actions\Product\Seller;

use App\Enums\Product\StatusEnum;
use App\Models\Product\Product;

class UpdateProduct
{
    /**
     * Handle the update of a new product.
     *
     * @param array $data Validated product data.
     * @return \App\Models\Product\Product The updated created product instance.
     */
    public function handle(array $data): Product
    {

        $product = $data["product"];

        if (isset($data['meta_keywords'])) {
            $keyWords = explode(',', $data['meta_keywords']);
            $product->keywords()->delete();

            // Create new keywords
            foreach ($keyWords as $value) {
                $value = trim($value);
                if (!empty($value)) {
                    $product->keywords()->create(['title' => $value]);
                }
            }
        }

        if (isset($data['meta_keywords'])) {

            $product->details()->delete();

            foreach ($data['details'] as $detail) {
                $product->details()->create([
                    'key' => $detail['key'],
                    'value' => $detail['value'],
                ]);
            }
        }



        $data['admin_status'] = StatusEnum::PENDING;

        $data['seller_status'] = isset($data['seller_status'])
            ? StatusEnum::fromString($data['seller_status'])
            : StatusEnum::PENDING;

        $product->update($data);

        return $product;
    }
}
