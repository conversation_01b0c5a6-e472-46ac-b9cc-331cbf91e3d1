<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the cart_items table in MongoDB.
 *
 * This migration creates a table to store individual items in shopping carts.
 * Each cart item references a shopping cart and contains product information.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     *
     * Creates the cart_items table with fields for cart reference,
     * product information, quantity, and timestamps.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shopping_cart_id'); // FK to shopping_carts.id
            $table->foreignId('product_id'); // Reference to the product
            $table->foreignId('product_variant_id'); // Reference to the product variation

            $table->integer('quantity'); // Quantity of this item in the cart
            $table->double('price'); // Snapshot of the product price at the time of adding to cart
            $table->double('sale_price')->nullable(); // Snapshot of the product price at the time of adding to cart
            $table->string('name'); // Snapshot of the product name
            $table->string('image')->nullable(); // Snapshot of the product image URL

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * Removes the cart_items table if it exists.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};

