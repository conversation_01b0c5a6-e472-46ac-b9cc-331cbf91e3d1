<?php
namespace App\Services\Actions\Categories;

use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Collection;

class GetCategoryTree
{
    /**
     * Get categories as a simple nested array tree
     *
     * @param Category|null $categoryModel
     * @return Collection
     */
    public function handle(?Category $categoryModel = null): Collection
    {
        // If a specific category is provided, get its children
        // Otherwise, get all root categories (no parent)
        if ($categoryModel) {
            $categories = $categoryModel->children()->get();
        } else {
            $categories = Category::whereNull("parent_id")->get();
        }

        $output = new Collection();

        foreach ($categories as $category) {
            $categoryData = [
                "id" => $category->id,
                "slug" => $category->slug,
                "title" => $category->title,
                "children" => []
            ];

            // Recursively get children if they exist
            if ($category->children()->count() > 0) {
                $categoryData["children"] = $this->handle($category);
            }

            $output->push($categoryData);
        }

        return $output;
    }
}