<?php

namespace App\Http\Requests\Cart;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Get Cart Request
 *
 * Validates the request data for retrieving the shopping cart.
 */
class GetCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can view their cart
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'discount_code' => 'sometimes|string|exists:discount_codes,code'
        ];
    }

    // No need to prepare data for validation as we'll get the user ID directly in the action
}
