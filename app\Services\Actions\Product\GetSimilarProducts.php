<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve similar products based on categories.
 */
class GetSimilarProducts
{
    /**
     * Retrieve similar products for a product by its slug.
     * Similar products are those that share categories with the source product.
     *
     * @param array $data An array containing:
     *                    - : The slug of the source product
     *                    - limit: (optional) Maximum number of similar products to return (default: 5)
     * @return Collection Collection of similar products
     */
    public function handle(array $data): Collection
    {
        // Set default value for limit
        $limit = $data['limit'] ?? 5;

        // Find the product by slug
        $product = Product::where('slug', $data['slug'])->first();

        // If product not found, return empty collection
        if (!$product) {
            return new Collection();
        }

        // Get the product's categories
        $categoryId = $product->category->id;



        // Find other products in the same categories, excluding the current product
        // First try to find products that match all categories
        $similarProducts = Product::where('id', '!=', (string) $product->id)
            ->where('category_id', $categoryId)
            ->with([
                'gallery',
                'variants',
            ])
            ->take($limit)
            ->get();

        // If we don't have enough products, try to find products from the same shop
        if ($similarProducts->count() < $limit && $product->shop_id) {
            $remainingLimit = $limit - $similarProducts->count();
            $existingIds = $similarProducts->pluck('id')->push($product->id)->toArray();

            $shopProducts = Product::where('shop_id', $product->shop_id)
                ->whereNotIn('id', $existingIds)
                ->with([
                    'gallery',
                    'variants',
                ])
                ->take($remainingLimit)
                ->get();

            $similarProducts = $similarProducts->concat($shopProducts);
        }

        // If we still don't have enough products, just get random products
        if ($similarProducts->count() < $limit) {
            $remainingLimit = $limit - $similarProducts->count();
            $existingIds = $similarProducts->pluck('id')->push($product->id)->toArray();

            $randomProducts = Product::whereNotIn('id', $existingIds)
                ->with([
                    'gallery',
                    'variants',
                ])
                ->take($remainingLimit * 3) // Get more than we need so we can shuffle
                ->get()
                ->shuffle() // MongoDB doesn't support inRandomOrder, so we fetch and shuffle
                ->take($remainingLimit);

            $similarProducts = $similarProducts->concat($randomProducts);
        }

        return $similarProducts;
    }
}
