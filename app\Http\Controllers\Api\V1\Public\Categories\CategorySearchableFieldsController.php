<?php

namespace App\Http\Controllers\Api\V1\Public\Categories;

use App\Http\Controllers\Controller;
use App\Services\Actions\Product\GetCategorySearchableAttributes;
use App\Http\Resources\Product\CategoryAttributeResource;
use App\Http\Requests\Catergories\GetCategorySearchableAttributesRequest;
use App\Http\Controllers\Api\BaseController;



class CategorySearchableFieldsController extends BaseController
{
    /**
     * Display the specified category's searchable attributes
     *
     * Returns all searchable attributes for the given category
     * and recursively includes attributes from all child categories.
     *
     * @param GetCategorySearchableAttributesRequest $request The validated request
     * @param GetCategorySearchableAttributes $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetCategorySearchableAttributesRequest $request, GetCategorySearchableAttributes $action)
    {
        $attributes = $action->handle($request->validated());
        return $this->sendResponse(
            CategoryAttributeResource::collection($attributes),
            __('messages.category.searchable_attributes_found')
        );
    }
}
