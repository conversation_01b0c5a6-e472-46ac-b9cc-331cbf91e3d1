<?php

namespace App\Services\Actions\Ticket;

use App\Exceptions\BusinessException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\Response;

class GetTicketMessages
{
    /**
     * gets a ticket messages
     * @param array $data
     * @return LengthAwarePaginator
     */
    public function handle(array $data): LengthAwarePaginator
    {
        $limit = $data['limit'] ?? 10;
        $page = $data['page'] ?? null;

        $userId = auth()->id();

        $ticket = $data['ticket'];

        if ($ticket->user_id != $userId) {
            throw new BusinessException(__('messages.ticket.unauthorized'), [], Response::HTTP_UNAUTHORIZED);
        }

        $messages = $ticket
            ->messages()
            ->with('user', 'gallery')
            ->latest()
            ->paginate($limit, ['*'], 'page', $page);

        return $messages;
    }
}
