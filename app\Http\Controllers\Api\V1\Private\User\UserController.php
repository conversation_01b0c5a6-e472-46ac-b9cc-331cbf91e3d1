<?php

namespace App\Http\Controllers\Api\V1\Private\User;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\User\UserResource;
use App\Services\Actions\User\GetUserProfile;
use App\Http\Requests\User\GetUserProfileRequest;

/**
 * Controller for managing user profile information
 *
 * @group User Management
 * @authenticated
 */
class UserController extends BaseController
{
    /**
     * Get the authenticated user's profile
     *
     * @param GetUserProfileRequest $request The validated request
     * @param GetUserProfile $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @authenticated
     */
    public function show(GetUserProfileRequest $request, GetUserProfile $action)
    {
        $user = $action->handle($request->validated());

        return $this->sendResponse(
            new UserResource($user),
            __('messages.user.profile_retrieved')
        );
    }
}
