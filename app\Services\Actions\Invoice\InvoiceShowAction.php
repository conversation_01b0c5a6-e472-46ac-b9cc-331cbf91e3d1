<?php

namespace App\Services\Actions\Invoice;

use App\Models\Shopping\Invoice;

/**
 * Action class for retrieving a specific invoice.
 */
class InvoiceShowAction
{
    /**
     * Retrieve a specific invoice by ID.
     *
     * @param array $data An array containing the invoice_id
     * @return Invoice The invoice with loaded relationships
     */
    public function handle(array $data): Invoice
    {
        // Find the invoice by ID with all necessary relationships for consistent product display
        return Invoice::with([
            'products.details',
            'products.productVariant.product.gallery',
            'products.productVariant.attributes',
            'transactions'
        ])
            ->where('id', $data['invoice_id'])
            ->where('user_id', auth()->id())
            ->firstOrFail();
    }
}
