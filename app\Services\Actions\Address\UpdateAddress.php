<?php

namespace App\Services\Actions\Address;

use App\Models\User\Address;

/**
 * UpdateAddress Action
 *
 * Updates an existing address for a user.
 */
class UpdateAddress
{
    /**
     * Handle updating an existing address.
     *
     * @param array $data [
     *   'address_id' => string,
     *   'name' => string,
     *   'receiver_name' => string,
     *   'receiver_phone' => string,
     *   'is_recipient_self' => boolean,
     *   'province' => string,
     *   'city' => string,
     *   'zip_code' => string,
     *   'address' => string,
     *   'latitude' => float|null,
     *   'longitude' => float|null,
     *   'address_model' => Address - The address model from the Form Request
     * ]
     * @return \App\Models\User\Address
     */
    public function handle(array $data): Address
    {
        // Get the address model from the Form Request
        $address = Address::find($data['address_id']);

        // Remove address_id from data before updating
        unset($data['address_id']);

        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Add user_id to the data array
        $data['user_id'] = $userId;

        // Update the address with the provided data
        $address->update($data);

        // Return the updated address
        return $address->fresh();
    }
}
