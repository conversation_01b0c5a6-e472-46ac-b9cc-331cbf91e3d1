<?php

namespace App\Http\Middleware;

use App\Models\User\User;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * API Key Authentication Middleware
 *
 * Authenticates users via API key from Authorization header.
 * Sets authenticated user in Lara<PERSON>'s auth system if valid key is provided.
 */
class LoginUserMiddleware
{
    private const BEARER_PREFIX = 'Bearer ';

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {

        try {
            $apiKey = $this->extractApiKey($request);
            if ($apiKey && $user = $this->findUserByApiKey($apiKey)) {
                $this->authenticateUser($request, $user);
            }

            return $next($request);
        } catch (Exception $e) {
            Log::warning('API authentication failed', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return $next($request);
        }
    }

    /**
     * Extract API key from Authorization header.
     */
    private function extractApiKey(Request $request): ?string
    {
        $authHeader = $request->header('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, self::BEARER_PREFIX)) {
            return null;
        }

        $key = substr($authHeader, strlen(self::BEARER_PREFIX));

        return !empty(trim($key)) ? $key : null;
    }

    /**
     * Find user by API key.
     */
    private function findUserByApiKey(string $apiKey): ?User
    {
        return User::where('api_key', $apiKey)->first();
    }

    /**
     * Authenticate the user in Laravel's auth system.
     */
    private function authenticateUser(Request $request, User $user): void
    {
        Auth::login($user);
        // Backward compatibility - attach user to request
        $request->merge(['user' => $user]);
        $request->setUserResolver(fn() => $user);
    }
}