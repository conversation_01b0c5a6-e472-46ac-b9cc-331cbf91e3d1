<?php

namespace Database\Seeders;

use App\Models\Shopping\DeliveryMethod;
use Illuminate\Database\Seeder;

/**
 * Seeder for creating delivery methods in the database.
 */
class DeliveryMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates three default delivery methods:
     * - Regular post delivery with random price
     * - In-store pickup (free)
     * - Express city delivery with random price
     */
    public function run(): void
    {
        DeliveryMethod::insert([
            [
                'title' => 'پست سفارشی',
                'price' => generateRandomFakePrice(4),
                'active' => true,
                'shop_id' => null,
            ],
            [
                'title' => 'دریافت حضوری از فروشگاه',
                'price' => 0.00,
                'active' => true,
                'shop_id' => null,
            ],
            [
                'title' => 'ارسال فوری درون‌شهری',
                'price' => generateRandomFakePrice(3),
                'active' => true,
                'shop_id' => null,
            ],
        ]);
    }
}
