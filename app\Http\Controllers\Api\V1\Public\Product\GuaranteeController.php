<?php

namespace App\Http\Controllers\Api\V1\Public\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\GetProductGuaranteesRequest;
use App\Http\Resources\Product\GuaranteeResource;
use App\Services\Actions\Product\GetProductGuarantees;

/**
 * Controller for retrieving product guarantees
 *
 * @group Product Management
 */
class GuaranteeController extends BaseController
{
    /**
     * Get guarantees available for a specific product
     *
     * @param GetProductGuaranteesRequest $request The validated request
     * @param GetProductGuarantees $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetProductGuaranteesRequest $request, GetProductGuarantees $action)
    {
        $guarantees = $action->handle($request->validated());

        return $this->sendResponse(
            GuaranteeResource::collection($guarantees),
            __('messages.guarantee.found')
        );
    }
}
