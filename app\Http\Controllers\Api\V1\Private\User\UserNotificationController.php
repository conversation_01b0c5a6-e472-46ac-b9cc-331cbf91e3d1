<?php

namespace App\Http\Controllers\Api\V1\Private\User;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\User\GetUserNotificationsRequest;
use App\Http\Resources\User\UserNotificationCollection;
use App\Services\Actions\User\GetUserNotifications;
use Illuminate\Http\JsonResponse;

class UserNotificationController extends BaseController
{

    public function index(GetUserNotificationsRequest $request, GetUserNotifications $action): JsonResponse
    {
        $userNotifications = $action->handle($request->validated());
        return $this->sendResponse(
            new UserNotificationCollection($userNotifications),
            __('messages.user.notifications_retrieved')
        );

    }
}
