<?php

namespace App\Services\Actions\Address;

use App\Models\User\Address;
use Illuminate\Database\Eloquent\Collection;

/**
 * GetUserAddresses Action
 *
 * Retrieves all addresses for a specific user.
 */
class GetUserAddresses
{
    /**
     * Handle retrieving the user's addresses.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function handle(array $data): Collection
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Get all addresses for the user
        return Address::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
