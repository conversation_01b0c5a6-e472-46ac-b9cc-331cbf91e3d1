<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;

class UpdateProductAttributes
{

    public function handle(array $data): Product
    {

        $product = Product::where('slug', $data["product_slug"])->first();
        $product->update(
            [
                "category_attributes" => $data["attributes"],
            ]
        );
        return $product;
    }
}
