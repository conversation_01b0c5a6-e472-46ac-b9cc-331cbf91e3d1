<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Discount\DiscountCode;

class DeactivateExpiredDiscountCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'discount-codes:deactivate-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deactivate expired discount codes';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // Update all expired discount codes to set 'is_active' to false
        $updatedRows = DiscountCode::where('expired_at', '<', now())
            ->where('is_active', true)
            ->update(['is_active' => false]);

        if ($updatedRows > 0) {
            $this->info("Deactivated {$updatedRows} expired discount codes.");
        } else {
            $this->info('No expired discount codes to deactivate.');
        }
    }
}