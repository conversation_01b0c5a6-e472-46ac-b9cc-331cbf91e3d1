<?php

namespace App\Http\Requests\Product;


use App\Enums\Product\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class CreateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'category_slug' => ['required', 'string', "exists:categories,slug"],
            'title' => ['sometimes', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'slug' => ['sometimes', 'string', 'max:255', 'unique:products,slug'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string', 'regex:/^([\pL\pN-]+)(,\s*[\pL\pN-]+)*$/u'],
            'details' => ['nullable', 'array', 'max:10'],
            'details.*.key' => ['required_with:details', 'string', 'max:25'],
            'details.*.value' => ['required_with:details', 'string', 'max:25'],
            'shop_id' => [
                'sometimes',
                'string',
                'exists:shops,id',

                //     // new UserBelongsToShop(),
            ],

            'images' => ['sometimes', 'array'],
            'images.*' => ['sometimes', 'image', 'max:5120'],
            'captions' => ['required_with:images', 'array'],
            'captions.*' => ['required', 'string', 'max:100'],
            'seller_status' => ['sometimes', 'string', StatusEnum::rule()],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'meta_keywords.regex' => __("messages.product.metakey_keywords_wrong_regex"),
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            "slug" => str_replace(" ", "-", $this->slug),
        ]);
    }
}
