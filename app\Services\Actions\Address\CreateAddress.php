<?php

namespace App\Services\Actions\Address;

use App\Models\User\Address;

/**
 * CreateAddress Actions
 *
 * Creates a new address for a user.
 */
class CreateAddress
{
    /**
     * Handle creating a new address.
     *
     * @param array $data [
     *   'name' => string,
     *   'receiver_name' => string,
     *   'receiver_phone' => string,
     *   'is_recipient_self' => boolean,
     *   'province' => string,
     *   'city' => string,
     *   'zip_code' => string,
     *   'address' => string,
     *   'latitude' => float|null,
     *   'longitude' => float|null
     * ]
     * @return \App\Models\User\Address
     */
    public function handle(array $data): Address
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Add user_id to the data array
        $data['user_id'] = $userId;

        // Create and return the new address
        return Address::create($data);
    }
}
