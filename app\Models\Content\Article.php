<?php

namespace App\Models\Content;

use App\Traits\Article\ArticleRelationsTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Article Model
 *
 * Represents an article with rich HTML content that can be associated with various models
 * through a polymorphic relationship. Used for storing content created with CKEditor.
 *
 * @property string $id MongoDB document ID
 * @property string $title The title of the article
 * @property string $content The HTML content of the article
 * @property string $articleable_type The class name of the parent model
 * @property string $articleable_id The ID of the parent model
 * @property \Carbon\Carbon $created_at When the article was created
 * @property \Carbon\Carbon $updated_at When the article was last updated
 */
class Article extends Model
{
    use ArticleRelationsTrait;
    /**
     * The database connection used by the model.
     *
     * @var string
     */
    

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'title',
        'content',
        'articleable_type',
        'articleable_id',
    ];


}
