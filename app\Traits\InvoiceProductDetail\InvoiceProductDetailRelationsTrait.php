<?php

namespace App\Traits\InvoiceProductDetail;

use App\Models\Shopping\InvoiceProduct;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * InvoiceProductDetail Relations Trait
 *
 * This trait contains all relationship methods for the InvoiceProductDetail model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\InvoiceProductDetail
 */
trait InvoiceProductDetailRelationsTrait
{
    /**
     * Get the invoice product that this detail belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoiceProduct()
    {
        return $this->belongsTo(InvoiceProduct::class, );
    }
}
