<?php

namespace App\Models\Ticket;

use App\Models\Content\Gallery;
use App\Models\User\User;
use App\Traits\Models\Helpers\ShamsiCraetedDate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class TicketMessage extends Model
{
    use HasFactory;
    use ShamsiCraetedDate;

    protected $fillable = [
        'ticket_id',
        'user_id',
        'message',
        'is_admin',
        'read_at',
    ];

    protected $casts = [
        'is_admin' => 'boolean',
        'read_at' => 'datetime',
    ];



    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    public function gallery(): MorphMany
    {
        return $this->MorphMany(Gallery::class, 'imageable');

    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    public function markAsRead(): void
    {
        $this->update(['read_at' => now()]);
    }

    public function markAsUnread(): void
    {
        $this->update(['read_at' => null]);
    }
}
