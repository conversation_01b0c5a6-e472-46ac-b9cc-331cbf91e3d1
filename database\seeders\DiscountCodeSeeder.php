<?php

namespace Database\Seeders;

use App\Enums\Discount\DiscountTypeEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Discount\DiscountCode;
use App\Models\User\User; // assumes you have a users table

class DiscountCodeSeeder extends Seeder
{
    public function run(): void
    {
        /** ----------------------------------------------------------------
         *  1) Global codes (no user_id)
         * ---------------------------------------------------------------- */
        $this->createCode(
            code: 'GLOBAL10',
            type: DiscountTypeEnum::PERCENT,
            amount: 10,
            isActive: true,
            expiresAt: now()->addDays(30)   // active, future expiry
        );

        $this->createCode(
            code: 'GLOBAL20OFF',
            type: DiscountTypeEnum::PERCENT,
            amount: 20,
            isActive: false,                // inactive, already expired
            expiresAt: now()->subDays(7)
        );

        $this->createCode(
            code: 'GLOBALFIX50K',
            type: DiscountTypeEnum::FIXED,
            amount: 50_000,
            isActive: true,                 // active, no expiry
            expiresAt: null
        );

        /** ----------------------------------------------------------------
         *  2) User-specific codes (attach to first 3 users found)
         * ---------------------------------------------------------------- */
        User::query()
            ->limit(3)
            ->get()
            ->each(function (User $user, int $index) {
                // Percent code
                $this->createCode(
                    code: "U{$user->id}_PERC",
                    type: DiscountTypeEnum::PERCENT,
                    amount: 15,
                    userId: $user->id,
                    isActive: true,
                    expiresAt: now()->addDays(14)
                );

                // Fixed code
                $this->createCode(
                    code: "U{$user->id}_FIX",
                    type: DiscountTypeEnum::FIXED,
                    amount: 100_000,
                    userId: $user->id,
                    isActive: true,
                    expiresAt: null
                );
            });
    }

    /** --------------------------------------------------------------------
     *  Helper to keep run() tidy
     * -------------------------------------------------------------------*/
    private function createCode(
        string $code,
        DiscountTypeEnum $type,
        int $amount,
        ?int $userId = null,
        bool $isActive = true,
        ?\DateTimeInterface $expiresAt = null
    ): void {
        DiscountCode::create([
            'uuid' => Str::uuid()->toString(),
            'code' => $code,
            'user_id' => $userId,
            'type' => $type,
            'amount' => $amount,
            'is_active' => $isActive,
            'expires_at' => $expiresAt,
        ]);
    }
}
