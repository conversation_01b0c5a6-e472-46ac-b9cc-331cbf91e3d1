<?php

namespace App\Services\Actions\Comment;

use App\Enums\Product\StatusEnum;
use App\Models\Content\Article;
use App\Models\UserInteraction\Comment;

class CreateArticleComment
{
    /**
     * Handle the creation of a new comment for an article.
     *
     * Finds the article by its ID, prepares the comment data by setting user and parent comment if applicable,
     * sets the comment status to 'pending', and creates the comment associated with the article.
     *
     * @param array $data An array of data required to create the comment. Expected keys include:
     *                    - 'article_id' (int): The ID of the article to which the comment belongs.
     *                    - 'reply_to' (int, optional): The ID of the parent comment if this is a reply.
     *                    - Other comment fields as per the Comment model.
     *
     * @return Comment The newly created Comment model instance.
     */
    public function handle(array $data): Comment
    {

        $article = Article::find($data["id"]);

        $commentData = $data;

        if (auth()->user())
            $commentData['user_id'] = auth()->id();

        if (isset($data['reply_to']))
            $commentData['parent_id'] = $data['reply_to'];

        $commentData['status'] = StatusEnum::PENDING;

        $comment = $article->comments()->create($commentData);
        return $comment;
    }


}
