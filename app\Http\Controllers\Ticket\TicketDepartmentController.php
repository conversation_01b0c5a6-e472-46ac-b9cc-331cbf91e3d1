<?php

namespace App\Http\Controllers\Ticket;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\Ticket\TicketDepartmentResource;
use App\Services\Actions\Ticket\GetAllTicketDepartments;
use Illuminate\Http\JsonResponse;

class TicketDepartmentController extends BaseController
{
    /**
     * gets all ticket Departments related to user
     * @param GetAllTicketDepartments $action
     * @return JsonResponse
     */
    public function index(GetAllTicketDepartments $action): JsonResponse
    {
        $tickets = $action->handle();
        return $this->sendResponse(
            TicketDepartmentResource::collection($tickets),
            __("messages.ticket.retrieved")
        );
    }
}
