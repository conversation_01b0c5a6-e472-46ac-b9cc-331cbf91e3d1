<?php

namespace App\Console\Commands;

use App\Models\Product\ProductVariant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateMissingPurchases extends Command
{
    protected $signature = 'products:generate-missing-purchases {--dry-run : Preview what would be created without making changes}';
    protected $description = 'Create purchases for products that have variants in stock but no purchases';

    public function handle()
    {
        $this->info('🔍 Checking variants for stock...');

        // Get variants that have stock > 0 and no existing purchases
        $variantsNeedingPurchases = ProductVariant::where('stock', '>', 0)
            ->whereDoesntHave('purchases')
            ->with(['product']) // Eager load to avoid N+1 queries
            ->get();

        if ($variantsNeedingPurchases->isEmpty()) {
            $this->info('✅ No variants found that need purchases created.');
            return Command::SUCCESS;
        }

        $this->info("📦 Found {$variantsNeedingPurchases->count()} variants needing purchases.");

        if ($this->option('dry-run')) {
            $this->showDryRunPreview($variantsNeedingPurchases);
            return Command::SUCCESS;
        }

        if (!$this->confirmGeneration($variantsNeedingPurchases->count())) {
            $this->info('❌ Operation cancelled.');
            return Command::SUCCESS;
        }

        $this->generatePurchases($variantsNeedingPurchases);

        $this->info('🎉 Successfully generated missing purchases!');
        return Command::SUCCESS;
    }

    private function showDryRunPreview($variants)
    {
        $this->info('📋 Dry run preview - The following purchases would be created:');
        $this->newLine();

        $headers = ['Variant ID', 'Product', 'Stock', 'Price', 'Purchase Date'];
        $rows = [];

        foreach ($variants as $variant) {
            $rows[] = [
                $variant->id,
                $variant->product?->name ?? 'Unknown Product',
                $variant->stock,
                number_format($variant->price, 2),
                now()->subDays(rand(0, 30))->format('Y-m-d'),
            ];
        }

        $this->table($headers, $rows);
    }

    private function confirmGeneration(int $count): bool
    {
        return $this->confirm("This will create {$count} purchase records. Continue?");
    }

    private function generatePurchases($variants)
    {
        $progressBar = $this->output->createProgressBar($variants->count());
        $progressBar->start();

        $purchasesData = [];

        foreach ($variants as $variant) {
            $purchasesData[] = [
                'quantity' => $variant->stock,
                'price' => $variant->price,
                'purchased_at' => now()->subDays(rand(0, 30)),
                'invoice_id' => null,
            ];
            $variant->purchases()->create($purchasesData);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
    }
}