<?php

namespace App\Services\Payment\Contracts;

use App\Exceptions\PaymentPreprocessException;

/**
 * Payment Gateway Contract
 *
 * Interface for payment gateway drivers that defines the required methods
 * for processing payments through different payment gateways.
 */
interface PaymentContract
{
    /**
     * Preprocess a payment with the payment gateway
     *
     * @param array $data Payment data including amount and description
     * @return array The preprocessed payment data including authority code
     * @throws PaymentPreprocessException If payment preprocessing fails
     */
    public function PaymentPreproccess(array $data): array;

    /**
     * Get the name of the payment gateway
     *
     * @return string The payment gateway name (e.g., 'zarinpal')
     */
    public function getPaymentGateWayName(): string;

    /**
     * Get the payment gateway URL for redirecting the user
     *
     * @param string $authority The authority code from the payment gateway
     * @return string The URL to redirect the user to complete the payment
     */
    public function getPaymentGatewayUrl(string $authority): string;

    /**
     * Get the validation rules for verifying a payment
     *
     * @return array The validation rules for the payment verification request
     */
    public function getGatewayVerificationRules(): array;

    /**
     * Verify a payment with the payment gateway
     *
     * @param array $data Verification data including authority and amount
     * @return array The verification result from the payment gateway
     * @throws PaymentPreprocessException If payment verification fails
     */
    public function verifyPayment(array $data): array;
}
