<?php

namespace App\Http\Controllers\Api\V1\Public\Shopping;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Transactions\VerifyTransactionRequest;
use App\Http\Resources\Shopping\VerifiedTransactionResource;
use App\Services\Actions\Transactions\VerifyTransaction;

/**
 * Controller for verifying payment transactions
 *
 * @group Transaction Verification
 * @unauthenticated
 */
class VerifyTransactionController extends BaseController
{
    /**
     * Verify a transaction after payment gateway callback
     *
     * @param VerifyTransactionRequest $request The validated request
     * @param VerifyTransaction $action The transaction verification action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(VerifyTransactionRequest $request, VerifyTransaction $action)
    {
        $transaction = $action->handle($request->validated());
        return $this->sendResponse(
            new VerifiedTransactionResource($transaction),
            __('messages.transaction.verified')
        );
    }
}
