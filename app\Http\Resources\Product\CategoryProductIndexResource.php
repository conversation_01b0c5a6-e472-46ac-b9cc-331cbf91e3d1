<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming category product index data into API responses.
 * Extends the basic ProductIndexResource with category metadata.
 */
class CategoryProductIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Pagination data
     * - Total products count
     * - Min and max prices
     * - Products array with essential information
     * - Category metadata
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $products = $this->resource['products'];
        return [
            'products' => ProductListItemResource::collection($products),
            'total_products' => $products->total(),
            'min_price' => (int) $this->resource['min_price'],
            'max_price' => (int) $this->resource['max_price'],
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'limit' => $products->perPage(),
                'total' => $products->total(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
            ],
        ];
    }
}
