<?php

use App\Enums\PayTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the transactions table in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {

            $table->id();
            $table->foreignId('user_id'); // ID of the user who owns this transaction
            $table->nullableMorphs('payable');
            $table->enum('status', PayTypeEnum::values())->default(PayTypeEnum::PENDING);
            $table->string('track_code')->nullable(); // Payment tracking code
            $table->double('amount'); // Transaction amount
            $table->string('payment_method'); // How payment was made (wallet, online, etc.)
            $table->string('payment_gateway')->nullable(); // Payment gateway used (stripe, zarinpal, etc.)
            $table->string('authority')->nullable(); // Payment authority code from payment gateway
            $table->string('ref_id')->nullable(); // Reference ID from payment gateway
            $table->string('description')->nullable(); // Description of the transaction
            $table->timestamp('paid_at')->nullable(); // When the payment was completed

            // Additional transaction details
            $table->string('source')->nullable(); // Source of the transaction (web, mobile, etc.)
            $table->string('terminal_id')->nullable(); // Terminal ID for POS transactions
            $table->string('ip')->nullable(); // IP address of the customer
            $table->string('card_number')->nullable(); // Masked card number
            $table->string('card_hash')->nullable(); // Hashed card number
            $table->double('fee')->nullable(); // Transaction fee
            $table->string('fee_type')->nullable(); // Type of fee (Merchant, Customer)
            $table->double('shaparak_fee')->nullable(); // Shaparak network fee
            $table->string('order_id')->nullable(); // Order ID from payment gateway
            $table->double('wages')->nullable(); // Wages amount if applicable
            $table->integer('code')->nullable(); // Response code from payment gateway

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
