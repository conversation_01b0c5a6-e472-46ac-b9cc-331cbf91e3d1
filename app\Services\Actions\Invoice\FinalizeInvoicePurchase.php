<?php

namespace App\Services\Actions\Invoice;

use App\Enums\Product\StatusEnum;
use App\Models\Product\PurchaseEntry;
use App\Models\Product\ProductsReservation;
use App\Models\Shopping\Invoice;
use Illuminate\Support\Facades\DB;

class FinalizeInvoicePurchase
{
    /**
     * Finalizes the purchase process for a given invoice.
     *
     * This method performs two critical operations for each product in the invoice:
     * 1. Reduces stock by creating a negative purchase entry in the ledger.
     * 2. Confirms the associated product reservation by updating its status.
     *
     * Notes:
     * - This method assumes the `$invoice->products` relationship is already loaded.
     * - The `quantity` is multiplied by -1 to represent stock reduction (i.e., a sale).
     * - Reservation notes include a generic success message in Persian.
     *
     * @param \App\Models\Shopping\Invoice $invoice The invoice being finalized. Must have `products` relationship loaded.
     *
     * @return void
     */
    public function handle(Invoice $invoice): void
    {
        // Load invoice products if not already loaded
        if (!$invoice->relationLoaded('products')) {
            $invoice->load('products');
        }
        foreach ($invoice->products as $invoiceProduct) {
            $effectivePrice = $invoiceProduct->effective_price;

            // 1. Reduce stock
            PurchaseEntry::create([
                'product_variant_id' => $invoiceProduct->product_variant_id,
                'quantity' => -1 * $invoiceProduct->quantity,
                'price' => $effectivePrice,
                'purchased_at' => now(),
                'invoice_id' => $invoice->id,
            ]);

            // 2. Confirm reservation
            ProductsReservation::where('invoice_id', $invoice->id)
                ->where('product_variant_id', $invoiceProduct->product_variant_id)

                ->update([
                    'status' => StatusEnum::CONFIRMED,
                    'note' => 'پرداخت با موفقیت انجام شد.',
                ]);
        }

    }
}
