<?php

namespace App\Models\Notification;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * FirebaseToken Model
 *
 * Represents a Firebase Cloud Messaging (FCM) token for a client device.
 * Used to send push notifications to specific devices.
 *
 * @property string $id MongoDB document ID
 * @property string $agent The user agent of the client device
 * @property string $ip The IP address of the client device
 * @property string $path The path where the token was registered
 * @property string $token The Firebase Cloud Messaging token
 * @property \Carbon\Carbon $created_at When the token was created
 * @property \Carbon\Carbon $updated_at When the token was last updated
 */
class FirebaseToken extends Model
{
    /**
     * The database connection used by the model.
     *
     * @var string
     */


    /**
     * The collection associated with the model.
     *
     * @var string
     */


    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'agent',
        'ip',
        'path',
        'token'
    ];
}