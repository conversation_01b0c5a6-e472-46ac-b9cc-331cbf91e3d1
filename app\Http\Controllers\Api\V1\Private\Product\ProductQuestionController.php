<?php

namespace App\Http\Controllers\Api\V1\Private\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\CreateProductQuestionRequest;
use App\Http\Resources\UserInteraction\QuestionResource;
use App\Services\Actions\Product\CreateProductQuestion;
use Symfony\Component\HttpFoundation\JsonResponse;

class ProductQuestionController extends BaseController
{
    /**
     * Store a newly created product question.
     *
     * This method handles the incoming request to create a new question
     * related to a product, using validated request data and the action class.
     *
     * @param CreateProductQuestionRequest $request The incoming request containing validated question data.
     * @param CreateProductQuestion $action The action class responsible for creating the product question.
     * @return \Illuminate\Http\JsonResponse A JSON response containing the created question resource and a success message.
     */

    public function store(CreateProductQuestionRequest $request, CreateProductQuestion $action): JsonResponse
    {
        $question = $action->handle($request->validated());

        return $this->sendResponse(
            new QuestionResource($question),
            __('messages.comments.created')
        );
    }
}

