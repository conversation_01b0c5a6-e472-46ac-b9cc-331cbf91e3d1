<?php

use App\Enums\Product\StatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // database/migrations/xxxx_xx_xx_create_questions_collection.php

        Schema::create('questions', function ($table) {
            $table->id();
            $table->morphs('questionable');
            $table->foreignId('user_id');
            $table->string('body');
            $table->enum('status', StatusEnum::values())->default(StatusEnum::PENDING);
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
